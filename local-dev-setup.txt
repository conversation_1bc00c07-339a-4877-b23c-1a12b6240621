## إعداد بيئة التطوير المحلية للإضافة المدفوعة

هذه الإضافة متاحة فقط كنسخة مدفوعة مع خطة مدى الحياة. لتطوير الإضافة محلياً مع Freemius، يجب إضافة الثوابت التالية إلى ملف wp-config.php في موقع WordPress المحلي:

```php
define( 'WP_FS__DEV_MODE', true );
define( 'WP_FS__SKIP_EMAIL_ACTIVATION', true );
define( 'WP_FS__sm-form_SECRET_KEY', 'sk_IF+TRnK3$U}xeDC$#1IzgH.{T-wWz' );
```

### خطوات التطوير:

1. أضف الثوابت المذكورة أعلاه إلى ملف wp-config.php
2. قم بتفعيل الإضافة
3. ستظهر شاشة الاشتراك، انقر على زر "السماح والمتابعة" للاتصال بواجهة برمجة التطبيقات (API) لأول مرة
4. بعد ذلك، يمكنك البدء في تطوير الإضافة

### ملاحظات هامة:

- الإضافة مدفوعة بالكامل مع خطة مدى الحياة فقط
- لا توجد نسخة مجانية من الإضافة
- تم إضافة تحقق من صلاحية الترخيص في جميع وظائف الإضافة
- إذا تم إلغاء الترخيص من لوحة تحكم Freemius، ستتوقف الإضافة عن العمل تماماً
- يتم التحقق من صلاحية الترخيص باستخدام الوظيفة `is_license_valid()`

### إعدادات Freemius:

تم تكوين Freemius بالإعدادات التالية:
- `is_premium` = true
- `has_premium_version` = false
- `has_free_plans` = false
- `has_paid_plans` = true

هذه الإعدادات تضمن أن الإضافة ستكون متاحة فقط كنسخة مدفوعة مع خطة مدى الحياة.
