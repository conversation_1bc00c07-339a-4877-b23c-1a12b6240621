<?php
/**
 * Plugin Name: Custom Order Form for WooCommerce
 * Description: Adds a custom order form to WooCommerce product pages.
 * Version: 1.1.3
 * Author: pexlat
 * Text Domain: custom-order-form
 */

if (!defined('ABSPATH')) {
    exit;
}

if (function_exists('sf_fs')) {
    sf_fs()->set_basename(true, __FILE__);
} else {
    /**
     * DO NOT REMOVE THIS IF, IT IS ESSENTIAL FOR THE
     * `function_exists` CALL ABOVE TO PROPERLY WORK.
     */
    if (!function_exists('sf_fs')) {
        // Create a helper function for easy SDK access.
        function sf_fs() {
            global $sf_fs;

            if (!isset($sf_fs)) {
                // Activate multisite network integration.
                if (!defined('WP_FS__PRODUCT_18847_MULTISITE')) {
                    define('WP_FS__PRODUCT_18847_MULTISITE', true);
                }

                // Include Freemius SDK.
                require_once dirname(__FILE__) . '/Freemius/start.php';

                $sf_fs = fs_dynamic_init(array(
                    'id'                  => '18847',
                    'slug'                => 'sm-form',
                    'type'                => 'plugin',
                    'public_key'          => 'pk_0b8e79c33a70ef618b1dc7a9a2059',
                    'is_premium'          => true,
                    'premium_suffix'      => 'unlimited',
                    // إعداد الإضافة كمدفوعة بالكامل بدون نسخة مجانية
                    'has_premium_version' => false,
                    'has_free_plans'      => false,
                    'has_addons'          => false,
                    'has_paid_plans'      => true,
                    'is_org_compliant'    => false,
                    'menu'                => array(
                        'first-path'     => 'plugins.php',
                        'contact'        => false,
                        'support'        => false,
                    ),
                ));
            }

            return $sf_fs;
        }

        // Init Freemius.
        sf_fs();
        // Signal that SDK was initiated.
        do_action('sf_fs_loaded');
    }

    // Include admin class
    require_once plugin_dir_path(__FILE__) . 'admin/class-custom-order-form-admin.php';

    // دالة للحصول على اسم النطاق الحالي
    function get_current_domain() {
        $domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
        if (empty($domain) && isset($_SERVER['SERVER_NAME'])) {
            $domain = $_SERVER['SERVER_NAME'];
        }

        // إزالة www. من بداية النطاق إن وجدت
        $domain = preg_replace('/^www\./', '', $domain);

        return $domain;
    }

    // دالة للتحقق من النطاق المرخص
    function is_licensed_domain() {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');

        // إذا لم يتم تخزين النطاق المرخص بعد، قم بتخزينه
        if (empty($licensed_domain) && function_exists('sf_fs') && sf_fs()->is_paying()) {
            update_option('custom_order_form_licensed_domain', $current_domain);
            return true;
        }

        // التحقق من تطابق النطاق الحالي مع النطاق المرخص
        return $current_domain === $licensed_domain;
    }

    // إضافة وظيفة للتحقق من صلاحية الترخيص
    function is_license_valid() {
        // التحقق مما إذا كنا في صفحة تفعيل الترخيص
        global $pagenow;
        $is_activation_page = (
            is_admin() &&
            (
                // صفحة الإضافات
                $pagenow === 'plugins.php' ||
                // صفحة تفعيل الترخيص
                (isset($_GET['page']) && strpos($_GET['page'], 'sm-form') !== false)
            )
        );

        // إذا كنا في صفحة التفعيل، نعتبر الترخيص صالح لتجنب المشاكل
        if ($is_activation_page) {
            return true;
        }

        // التحقق من أن Freemius مهيأ
        if (!function_exists('sf_fs')) {
            return false;
        }

        // التحقق من أن المستخدم لديه ترخيص صالح وأن النطاق مرخص
        return sf_fs()->is_paying() && is_licensed_domain();
    }

    // إضافة وظيفة لإيقاف الإضافة إذا لم يكن الترخيص صالحاً
    function check_license_and_disable_plugin() {
        // تحقق مما إذا كنا في صفحة تفعيل الترخيص
        global $pagenow;
        $is_activation_page = (
            is_admin() &&
            (
                // صفحة الإضافات
                $pagenow === 'plugins.php' ||
                // صفحة تفعيل الترخيص
                (isset($_GET['page']) && strpos($_GET['page'], 'sm-form') !== false)
            )
        );

        // إذا كنا في صفحة التفعيل، لا نقوم بإيقاف الإضافة
        if ($is_activation_page) {
            return;
        }

        if (!is_license_valid()) {
            // إيقاف وظائف الإضافة في الواجهة الأمامية فقط
            remove_shortcode('custom-order-form');
            remove_action('woocommerce_single_product_summary', 'add_custom_order_form', 20);

            // إضافة إشعار في لوحة التحكم
            add_action('admin_notices', 'license_invalid_notice');
        }
    }

    // إضافة إشعار في لوحة التحكم
    function license_invalid_notice() {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        ?>
        <div class="notice notice-error">
            <?php if ($is_domain_mismatch): ?>
                <p><?php _e('إضافة "فورم الطلب" غير مفعلة. الترخيص الحالي مرتبط بنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق (' . esc_html($current_domain) . ').', 'custom-order-form'); ?></p>
            <?php else: ?>
                <p><?php _e('إضافة "فورم الطلب" غير مفعلة. يرجى تفعيل ترخيص صالح لاستخدام الإضافة.', 'custom-order-form'); ?></p>
            <?php endif; ?>
        </div>
        <?php
    }

    // تهيئة الإضافة
    function custom_order_form_init() {
        // تهيئة الإضافة دائماً في لوحة التحكم
        if (is_admin()) {
            $plugin = new Custom_Order_Form_Admin('custom-order-form', '1.0');
        } else {
            // في الواجهة الأمامية، نتحقق من صلاحية الترخيص
            if (is_license_valid()) {
                $plugin = new Custom_Order_Form_Admin('custom-order-form', '1.0');
            } else {
                // إيقاف وظائف الإضافة في الواجهة الأمامية فقط
                check_license_and_disable_plugin();
            }
        }
    }
    add_action('plugins_loaded', 'custom_order_form_init');

    // تحديث النطاق المرخص عند تفعيل الترخيص
    function update_licensed_domain() {
        if (function_exists('sf_fs') && sf_fs()->is_paying()) {
            update_option('custom_order_form_licensed_domain', get_current_domain());
        }
    }

    // التحقق من صلاحية الترخيص عند تغيير حالة الترخيص
    add_action('fs_after_license_change', 'check_license_and_disable_plugin');
    add_action('fs_after_license_change', 'update_licensed_domain');

    // تخصيص واجهة تفعيل الترخيص
    function customize_freemius_connect_message($message, $user_first_name, $plugin_title, $user_login, $site_link, $freemius_link) {
        return sprintf(
            __('Welcome to %s! To get started, please enter your license key:', 'custom-order-form'),
            '<b>' . $plugin_title . '</b>'
        );
    }
    add_filter('fs_connect_message_on_update', 'customize_freemius_connect_message', 10, 6);
    add_filter('fs_connect_message', 'customize_freemius_connect_message', 10, 6);

    // إخفاء معلومات التشخيص
    function hide_freemius_diagnostic_info() {
        return false;
    }
    add_filter('fs_show_full_site_info', 'hide_freemius_diagnostic_info');
    add_filter('fs_show_plugins_and_themes', 'hide_freemius_diagnostic_info');
    add_filter('fs_show_site_info', 'hide_freemius_diagnostic_info');

    // إخفاء رابط النسخة المجانية
    function hide_freemius_free_version_link() {
        return '';
    }
    add_filter('fs_connect_message_on_update', 'hide_freemius_free_version_link', 10, 6);

    // تخصيص نص زر التفعيل
    function customize_freemius_activate_button($text) {
        return __('Activate License', 'custom-order-form');
    }
    add_filter('fs_connect_button_text', 'customize_freemius_activate_button');

    // إخفاء روابط إضافية
    function hide_freemius_additional_links($html) {
        return '';
    }
    add_filter('fs_connect_additional_links', 'hide_freemius_additional_links');

    // إخفاء نص الخصوصية والشروط
    function hide_freemius_privacy_policy_and_tos_links() {
        return '';
    }
    add_filter('fs_privacy_policy_and_tos_links', 'hide_freemius_privacy_policy_and_tos_links');

    // إخفاء نص "Powered by Freemius"
    function hide_freemius_powered_by() {
        return '';
    }
    add_filter('fs_powered_by_text', 'hide_freemius_powered_by');

    // إخفاء رابط "Can't find your license key?"
    function hide_freemius_cant_find_license() {
        return '';
    }
    add_filter('fs_license_key_text', 'hide_freemius_cant_find_license');

    // إخفاء رابط "License issues?"
    function hide_freemius_license_issues() {
        return '';
    }
    add_filter('fs_why_so_fast_text', 'hide_freemius_license_issues');

    // إخفاء نص "For delivery of security & feature updates..."
    function hide_freemius_permission_message() {
        return '';
    }
    add_filter('fs_permission_manager_description', 'hide_freemius_permission_message');

    // تخصيص CSS لواجهة تفعيل الترخيص
    function customize_freemius_activation_page_css() {
        echo '<style>
            /* تنسيق الحاوية الرئيسية */
            #fs_connect {
                max-width: 500px !important;
                margin: 50px auto !important;
                padding: 30px !important;
                background-color: #fff !important;
                border-radius: 10px !important;
                box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
            }

            /* تنسيق العنوان */
            .fs-plugin-title, .fs-plugin-title + h2 {
                font-size: 24px !important;
                margin-bottom: 20px !important;
                color: #2563eb !important;
                text-align: center !important;
                font-weight: 600 !important;
            }

            /* تنسيق حقل إدخال الترخيص */
            .fs-license-key-container {
                margin: 30px 0 !important;
            }

            #fs_license_key {
                width: 100% !important;
                padding: 12px !important;
                font-size: 16px !important;
                border: 1px solid #ddd !important;
                border-radius: 5px !important;
                box-shadow: none !important;
            }

            /* تنسيق زر التفعيل */
            .fs-actions {
                margin-top: 30px !important;
                text-align: center !important;
            }

            .fs-actions .button {
                padding: 10px 30px !important;
                height: auto !important;
                font-size: 16px !important;
                background-color: #2563eb !important;
                border-color: #2563eb !important;
                color: #fff !important;
                border-radius: 5px !important;
                box-shadow: none !important;
                transition: all 0.3s ease !important;
            }

            .fs-actions .button:hover {
                background-color: #1d4ed8 !important;
                border-color: #1d4ed8 !important;
            }

            /* إخفاء العناصر غير المرغوب فيها */
            .fs-permissions,
            .fs-freemium-licensing,
            .fs-terms,
            .fs-trigger,
            .fs-connect-options,
            .fs-header,
            .fs-visual,
            .fs-content .fs-error,
            .fs-content .fs-notice,
            .fs-other-option,
            .fs-skip {
                display: none !important;
            }

            /* تنسيق رسالة الخطأ */
            .fs-content .fs-error {
                display: block !important;
                margin: 10px 0 !important;
                padding: 10px !important;
                background-color: #fee2e2 !important;
                border-left: 4px solid #ef4444 !important;
                color: #b91c1c !important;
            }
        </style>';
    }
    add_action('admin_head', 'customize_freemius_activation_page_css');

    // تخصيص نص حقل إدخال الترخيص
    function customize_freemius_license_key_label() {
        return __('License key', 'custom-order-form');
    }
    add_filter('fs_connect_license_key_text', 'customize_freemius_license_key_label');

    // إخفاء زر تخطي التفعيل
    function hide_freemius_skip_button() {
        return '';
    }
    add_filter('fs_connect_skip_text', 'hide_freemius_skip_button');

    // إخفاء خيار "Opt out"
    function hide_freemius_opt_out() {
        return false;
    }
    add_filter('fs_is_extensions_tracking_allowed', 'hide_freemius_opt_out');
    add_filter('fs_is_tracking_allowed', 'hide_freemius_opt_out');

    // تخصيص رسالة الخطأ عند إدخال مفتاح ترخيص غير صالح
    function customize_freemius_invalid_license_message($message) {
        return __('Invalid license key. Please check your license key and try again.', 'custom-order-form');
    }
    add_filter('fs_api_error_license_key', 'customize_freemius_invalid_license_message');

    // إضافة JavaScript لإخفاء العناصر الإضافية التي قد تظهر بعد تحميل الصفحة
    function add_freemius_custom_js() {
        echo '<script type="text/javascript">
            jQuery(document).ready(function($) {
                // إخفاء العناصر غير المرغوب فيها
                $(".fs-permissions, .fs-freemium-licensing, .fs-terms, .fs-trigger, .fs-connect-options, .fs-header, .fs-visual, .fs-other-option, .fs-skip").hide();

                // إظهار رسالة الخطأ إذا كانت موجودة
                $(".fs-content .fs-error").show();

                // تعديل نص العنوان
                $(".fs-plugin-title, .fs-plugin-title + h2").text("Welcome to Custom Order Form for WooCommerce! To get started, please enter your license key:");
            });
        </script>';
    }
    add_action('admin_footer', 'add_freemius_custom_js');
}

function custom_order_form_shortcode() {
    // التحقق من صلاحية الترخيص قبل عرض النموذج
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            return '<p>' . __('هذه الإضافة مرخصة لنطاق آخر. يرجى شراء ترخيص جديد لهذا النطاق.', 'custom-order-form') . '</p>';
        } else {
            return '<p>' . __('هذه الإضافة تتطلب ترخيص صالح للعمل.', 'custom-order-form') . '</p>';
        }
    }

    ob_start();
    custom_order_form_assets();
    add_custom_order_form();
    return ob_get_clean();
}
add_shortcode('custom-order-form', 'custom_order_form_shortcode');

function check_customer_block() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            wp_send_json_error('هذه الإضافة مرخصة لنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق.');
        } else {
            wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        }
        return;
    }

    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $ip = $_SERVER['REMOTE_ADDR'];

    if (!$phone && !$ip) {
        wp_send_json_error('بيانات غير صالحة');
        return;
    }

    $blocked_items = get_option('custom_order_form_blocked_items', array());
    $is_blocked = false;

    foreach ($blocked_items as $item) {
        if (($item['type'] === 'phone' && $item['value'] === $phone) ||
            ($item['type'] === 'ip' && $item['value'] === $ip)) {
            $is_blocked = true;
            break;
        }
    }

    wp_send_json_success(array('blocked' => $is_blocked));
}

function is_customer_blocked($phone) {
    $ip = $_SERVER['REMOTE_ADDR'];
    $blocked_items = get_option('custom_order_form_blocked_items', array());

    foreach ($blocked_items as $item) {
        if (($item['type'] === 'phone' && $item['value'] === $phone) ||
            ($item['type'] === 'ip' && $item['value'] === $ip)) {
            return true;
        }
    }

    return false;
}

function place_custom_order() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            wp_send_json_error('هذه الإضافة مرخصة لنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق.');
        } else {
            wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        }
        return;
    }

    // التحقق من حد الطلبات اليومي
    $spam_settings = get_option('custom_order_form_spam_settings', array(
        'limit_orders' => false
    ));

    if ($spam_settings['limit_orders']) {
        $user_ip = $_SERVER['REMOTE_ADDR'];
        $last_order = get_transient('last_order_' . $user_ip);
        if ($last_order) {
            $time_left = human_time_diff(time(), $last_order + (24 * HOUR_IN_SECONDS));
            wp_send_json_error(sprintf('عذراً، يمكنك إرسال طلب جديد بعد %s', $time_left));
            return;
        }
    }

    // التحقق من العملاء المحظورين
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    if (is_customer_blocked($phone)) {
        wp_send_json_error('عذراً، لا يمكنك إرسال طلبات في الوقت الحالي');
        return;
    }

    if (isset($_POST['product_id'])) {
        $product_id = intval($_POST['product_id']);
        $product = wc_get_product($product_id);
        $price = isset($_POST['product_price']) ? floatval($_POST['product_price']) : $product->get_price();
        $full_name = sanitize_text_field($_POST['fullName']);
        $phone = sanitize_text_field($_POST['phone']);
        $country = sanitize_text_field($_POST['country']);
        $city = sanitize_text_field($_POST['city']);
        $address = sanitize_text_field($_POST['address']);
        $delivery_type = sanitize_text_field($_POST['delivery_type']);
        $quantity = isset($_POST['quantity']) ? max(1, intval($_POST['quantity'])) : 1;

        // التحقق من البيانات المطلوبة
        if (empty($full_name) || empty($phone)) {
            wp_send_json_error('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // تجميع المتغيرات

        $variations = array();
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'attribute_') !== false) {
               $variations[$key] = sanitize_text_field($value);
            }
        }

        $order = wc_create_order();
        if (!$order) {
            wp_send_json_error('Failed to create order');
            return;
        }

        // إضافة المنتج مع المتغيرات إلى الطلب
        $order->add_product($product, $quantity, array('variation' => $variations));

        // تجميع العنوان الكامل مع نوع التوصيل
        $full_address = $address . ' (' . ($delivery_type == 'home' ? 'توصيل للمنزل' : 'توصيل للمكتب') . ')';

        // تقسيم الاسم الكامل إلى اسم أول واسم أخير
        $name_parts = explode(' ', $full_name);
        $first_name = array_shift($name_parts);
        $last_name = implode(' ', $name_parts);

        // إعداد عناوين الدفع والشحن
        $address_data = array(
            'first_name' => $first_name,
            'last_name' => $last_name,
            'phone' => $phone,
            'country' => 'DZ', // الدولة دائماً الجزائر
            'state' => $country, // الولاية (مع الرقم والاسم)
            'city' => $city, // البلدية
            'address_1' => $full_address,
        );

        $order->set_address($address_data, 'billing');
        $order->set_address($address_data, 'shipping');

        // حفظ بيانات إضافية
        $order->update_meta_data('_customer_full_name', $full_name);

        // حذف الطلب المتروك بعد نجاح إنشاء الطلب
        $abandoned_orders = get_option('custom_order_form_abandoned_orders', array());
        $user_ip = $_SERVER['REMOTE_ADDR'];
        $abandoned_orders = array_filter($abandoned_orders, function($abandoned_order) use ($user_ip) {
            return !isset($abandoned_order['ip']) || $abandoned_order['ip'] !== $user_ip;
        });
        update_option('custom_order_form_abandoned_orders', array_values($abandoned_orders));

        // إضافة رسوم الشحن
        $shipping_cost = isset($_POST['shipping_cost']) ? floatval($_POST['shipping_cost']) : 0;
        if ($shipping_cost > 0) {
            $item = new WC_Order_Item_Shipping();
            $item->set_method_title('رسوم التوصيل');
            $item->set_total($shipping_cost);
            $order->add_item($item);
        }

        // تحديث بيانات الطلب وحساب الإجمالي
        $order->update_meta_data('delivery_type', $delivery_type);
        $order->calculate_totals();

        // تحديث المخزون بشكل صريح للمنتجات المتغيرة
        if ($product->is_type('variable') && !empty($variations)) {
            $data_store = WC_Data_Store::load('product');
            $variation_id = $data_store->find_matching_product_variation($product, $variations);

            if ($variation_id > 0) {
                // استخدام الدالة المساعدة لتحديث المخزون
                update_variation_stock($variation_id, $quantity);

                // تسجيل تغيير المخزون في سجل الطلب
                wc_maybe_reduce_stock_levels($order->get_id());
            }
        }

        $order->update_status('processing');
        $order->save();

        // حفظ وقت آخر طلب
        if ($spam_settings['limit_orders']) {
            $user_ip = $_SERVER['REMOTE_ADDR'];
            set_transient('last_order_' . $user_ip, time(), 24 * HOUR_IN_SECONDS);
        }

        // إعادة توجيه المستخدم بعد نجاح الطلب
        wp_send_json_success(array(
            'redirect_url' => $order->get_checkout_order_received_url()
        ));
    } else {
        wp_send_json_error('Product ID not set');
    }
}

// معالجة طلب سعر المتغير
function get_variation_price() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            wp_send_json_error('هذه الإضافة مرخصة لنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق.');
        } else {
            wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        }
        return;
    }

    if (!isset($_POST['product_id'])) {
        wp_send_json_error('Product ID not set');
        return;
    }

    $product_id = intval($_POST['product_id']);
    $product = wc_get_product($product_id);

    if (!$product || !$product->is_type('variable')) {
        wp_send_json_error('Invalid product');
        return;
    }

    $variation_attributes = array();
    $all_attributes_set = true;

    foreach ($_POST as $key => $value) {
        if (strpos($key, 'attribute_') !== false) {
            if (empty($value)) {
                $all_attributes_set = false;
            }
            $variation_attributes[$key] = sanitize_text_field($value);
        }
    }

    // إذا لم يتم تحديد جميع المتغيرات، نرجع السعر الأدنى
    if (!$all_attributes_set) {
        wp_send_json_success(array(
            'price' => $product->get_variation_price('min')
        ));
        return;
    }

    $data_store = WC_Data_Store::load('product');
    $variation_id = $data_store->find_matching_product_variation($product, $variation_attributes);

    if ($variation_id) {
        $variation = wc_get_product($variation_id);
        $price = $variation->get_price();
        $regular_price = $variation->get_regular_price();

        // التحقق مما إذا كان المتغير يحتوي على تخفيض
        $has_discount = $variation->is_on_sale() && $regular_price > $price;

        wp_send_json_success(array(
            'price' => $price,
            'regular_price' => $has_discount ? $regular_price : null,
            'is_variation' => true,
            'has_discount' => $has_discount
        ));
    } else {
        // إذا لم يتم العثور على تطابق، نرجع السعر الأدنى
        $min_price = $product->get_variation_price('min');
        $min_regular_price = $product->get_variation_regular_price('min');

        // التحقق مما إذا كان هناك تخفيض على الأسعار الدنيا
        $has_discount = $min_regular_price > $min_price;

        wp_send_json_success(array(
            'price' => $min_price,
            'regular_price' => $has_discount ? $min_regular_price : null,
            'has_discount' => $has_discount
        ));
    }
}

add_action('wp_ajax_get_variation_price', 'get_variation_price');
add_action('wp_ajax_nopriv_get_variation_price', 'get_variation_price');

// التحقق من حالة المخزون للمتغيرات
function check_variation_stock() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        return;
    }

    if (!isset($_POST['product_id'])) {
        wp_send_json_error('Product ID not set');
        return;
    }

    $product_id = intval($_POST['product_id']);
    $product = wc_get_product($product_id);

    if (!$product) {
        wp_send_json_error('Invalid product');
        return;
    }

    // التحقق من إعدادات WooCommerce للسماح بالطلب في حالة نفاذ المخزون
    $allow_backorders = get_option('woocommerce_manage_stock') === 'yes' &&
                        get_option('woocommerce_backorders_allowed') === 'yes';

    // للمنتجات البسيطة، نرجع حالة المخزون مباشرة
    if (!$product->is_type('variable')) {
        $is_in_stock = $product->is_in_stock();
        $backorders_allowed = $product->backorders_allowed();
        $can_order = $is_in_stock || $backorders_allowed || $allow_backorders;
        $stock_quantity = $product->get_stock_quantity();
        $stock_quantity_text = $stock_quantity !== null ? ' - ' . $stock_quantity . ' قطعة متوفرة' : '';

        $stock_status = '';
        if ($is_in_stock) {
            $stock_status = '<span class="in-stock"><i class="fas fa-check-circle"></i> متوفر في المخزون' . $stock_quantity_text . '</span>';
        } else {
            if ($backorders_allowed || $allow_backorders) {
                $stock_status = '<span class="backorder"><i class="fas fa-clock"></i> متوفر للطلب المسبق</span>';
            } else {
                $stock_status = '<span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر في المخزون</span>';
            }
        }

        wp_send_json_success(array(
            'is_in_stock' => $is_in_stock,
            'backorders_allowed' => $backorders_allowed,
            'can_order' => $can_order,
            'stock_quantity' => $stock_quantity,
            'stock_status' => $stock_status
        ));
        return;
    }

    // للمنتجات المتغيرة، نبحث عن المتغير المطابق
    $variation_attributes = array();
    $all_attributes_set = true;

    foreach ($_POST as $key => $value) {
        if (strpos($key, 'attribute_') !== false) {
            if (empty($value)) {
                $all_attributes_set = false;
            }
            $variation_attributes[$key] = sanitize_text_field($value);
        }
    }

    // إذا لم يتم تحديد جميع المتغيرات، نرجع حالة المخزون العامة للمنتج
    if (!$all_attributes_set) {
        $is_in_stock = $product->is_in_stock();
        $backorders_allowed = $product->backorders_allowed();
        $can_order = $is_in_stock || $backorders_allowed || $allow_backorders;
        $stock_quantity = $product->get_stock_quantity();
        $stock_quantity_text = $stock_quantity !== null ? ' - ' . $stock_quantity . ' قطعة متوفرة' : '';

        $stock_status = '';
        if ($is_in_stock) {
            $stock_status = '<span class="in-stock"><i class="fas fa-check-circle"></i> متوفر في المخزون' . $stock_quantity_text . '</span>';
        } else {
            if ($backorders_allowed || $allow_backorders) {
                $stock_status = '<span class="backorder"><i class="fas fa-clock"></i> متوفر للطلب المسبق</span>';
            } else {
                $stock_status = '<span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر في المخزون</span>';
            }
        }

        wp_send_json_success(array(
            'is_in_stock' => $is_in_stock,
            'backorders_allowed' => $backorders_allowed,
            'can_order' => $can_order,
            'stock_quantity' => $stock_quantity,
            'stock_status' => $stock_status
        ));
        return;
    }

    $data_store = WC_Data_Store::load('product');
    $variation_id = $data_store->find_matching_product_variation($product, $variation_attributes);

    if ($variation_id) {
        $variation = wc_get_product($variation_id);
        $is_in_stock = $variation->is_in_stock();
        $backorders_allowed = $variation->backorders_allowed();
        $can_order = $is_in_stock || $backorders_allowed || $allow_backorders;
        $stock_quantity = $variation->get_stock_quantity();
        $stock_quantity_text = $stock_quantity !== null ? ' - ' . $stock_quantity . ' قطعة متوفرة' : '';

        $stock_status = '';
        if ($is_in_stock) {
            $stock_status = '<span class="in-stock"><i class="fas fa-check-circle"></i> متوفر في المخزون' . $stock_quantity_text . '</span>';
        } else {
            if ($backorders_allowed || $allow_backorders) {
                $stock_status = '<span class="backorder"><i class="fas fa-clock"></i> متوفر للطلب المسبق</span>';
            } else {
                $stock_status = '<span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر في المخزون</span>';
            }
        }

        wp_send_json_success(array(
            'is_in_stock' => $is_in_stock,
            'backorders_allowed' => $backorders_allowed,
            'can_order' => $can_order,
            'stock_quantity' => $stock_quantity,
            'stock_status' => $stock_status,
            'variation_id' => $variation_id
        ));
    } else {
        // إذا لم يتم العثور على متغير مطابق، نرجع حالة المخزون العامة للمنتج
        $is_in_stock = $product->is_in_stock();
        $backorders_allowed = $product->backorders_allowed();
        $can_order = $is_in_stock || $backorders_allowed || $allow_backorders;
        $stock_quantity = $product->get_stock_quantity();
        $stock_quantity_text = $stock_quantity !== null ? ' - ' . $stock_quantity . ' قطعة متوفرة' : '';

        $stock_status = '';
        if ($is_in_stock) {
            $stock_status = '<span class="in-stock"><i class="fas fa-check-circle"></i> متوفر في المخزون' . $stock_quantity_text . '</span>';
        } else {
            if ($backorders_allowed || $allow_backorders) {
                $stock_status = '<span class="backorder"><i class="fas fa-clock"></i> متوفر للطلب المسبق</span>';
            } else {
                $stock_status = '<span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر في المخزون</span>';
            }
        }

        wp_send_json_success(array(
            'is_in_stock' => $is_in_stock,
            'backorders_allowed' => $backorders_allowed,
            'can_order' => $can_order,
            'stock_quantity' => $stock_quantity,
            'stock_status' => $stock_status
        ));
    }
}

add_action('wp_ajax_check_variation_stock', 'check_variation_stock');
add_action('wp_ajax_nopriv_check_variation_stock', 'check_variation_stock');

// حفظ الطلب المتروك
function save_abandoned_order() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            wp_send_json_error('هذه الإضافة مرخصة لنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق.');
        } else {
            wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        }
        return;
    }

    if (!isset($_POST['order_data'])) {
        return;
    }

    $order_data = json_decode(stripslashes($_POST['order_data']), true);
    if (!$order_data || !isset($order_data['fullName'])) {
        return;
    }

    // إضافة معرف فريد وتاريخ للطلب
    $order_data['id'] = uniqid('abandoned_');
    $order_data['date'] = current_time('mysql');
    $order_data['ip'] = $_SERVER['REMOTE_ADDR'];

    // الحصول على الطلبات المتروكة الحالية
    $abandoned_orders = get_option('custom_order_form_abandoned_orders', array());

    // حذف الطلبات القديمة لنفس عنوان IP
    $abandoned_orders = array_filter($abandoned_orders, function($order) use ($order_data) {
        return !isset($order['ip']) || $order['ip'] !== $order_data['ip'];
    });

    // إضافة الطلب الجديد في البداية
    array_unshift($abandoned_orders, $order_data);

    // الاحتفاظ فقط بآخر 50 طلب
    $abandoned_orders = array_slice($abandoned_orders, 0, 50);

    update_option('custom_order_form_abandoned_orders', array_values($abandoned_orders));
}

add_action('wp_ajax_save_abandoned_order', 'save_abandoned_order');
add_action('wp_ajax_nopriv_save_abandoned_order', 'save_abandoned_order');

add_action('wp_ajax_check_customer_block', 'check_customer_block');
add_action('wp_ajax_nopriv_check_customer_block', 'check_customer_block');

// دالة مساعدة لتحديث المخزون للمنتجات المتغيرة
function update_variation_stock($variation_id, $quantity) {
    if ($variation_id > 0) {
        $variation = wc_get_product($variation_id);
        if ($variation && $variation->managing_stock()) {
            // تحديث كمية المخزون للمتغير
            $stock_quantity = $variation->get_stock_quantity();
            $new_stock = max(0, $stock_quantity - $quantity);
            $variation->set_stock_quantity($new_stock);
            $variation->set_stock_status($new_stock > 0 ? 'instock' : 'outofstock');
            $variation->save();

            // تحديث حالة المخزون في قاعدة البيانات
            wc_update_product_stock_status($variation_id, $new_stock > 0 ? 'instock' : 'outofstock');

            return true;
        }
    }
    return false;
}

add_action('wp_ajax_place_custom_order', 'place_custom_order');
add_action('wp_ajax_nopriv_place_custom_order', 'place_custom_order');

// إضافة المنتج للسلة مع البيانات المخصصة
function add_to_cart_with_custom_data() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            wp_send_json_error('هذه الإضافة مرخصة لنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق.');
        } else {
            wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        }
        return;
    }

    // التحقق من البيانات المطلوبة
    if (!isset($_POST['product_id']) || !isset($_POST['fullName']) || !isset($_POST['phone'])) {
        wp_send_json_error('بيانات غير مكتملة');
        return;
    }

    $product_id = intval($_POST['product_id']);
    $quantity = isset($_POST['quantity']) ? max(1, intval($_POST['quantity'])) : 1;

    // تجميع البيانات المخصصة
    $custom_data = array(
        'customer_data' => array(
            'full_name' => sanitize_text_field($_POST['fullName']),
            'phone' => sanitize_text_field($_POST['phone']),
            'country' => isset($_POST['country']) ? sanitize_text_field($_POST['country']) : '',
            'city' => isset($_POST['city']) ? sanitize_text_field($_POST['city']) : '',
            'address' => isset($_POST['address']) ? sanitize_text_field($_POST['address']) : '',
            'delivery_type' => isset($_POST['delivery_type']) ? sanitize_text_field($_POST['delivery_type']) : 'home',
            'shipping_cost' => isset($_POST['shipping_cost']) ? floatval($_POST['shipping_cost']) : 0
        )
    );

    // تجميع المتغيرات إذا كانت موجودة
    $variation_id = 0;
    $variations = array();
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'attribute_') !== false) {
            $variations[$key] = sanitize_text_field($value);
        }
    }

    // إذا كان المنتج متغيراً، ابحث عن معرف المتغير
    $product = wc_get_product($product_id);
    if ($product && $product->is_type('variable') && !empty($variations)) {
        $data_store = WC_Data_Store::load('product');
        $variation_id = $data_store->find_matching_product_variation($product, $variations);
    }

    // إضافة المنتج للسلة
    $cart_item_key = WC()->cart->add_to_cart(
        $product_id,
        $quantity,
        $variation_id,
        $variations,
        $custom_data
    );

    if ($cart_item_key) {
        // تحديث المخزون للمنتجات المتغيرة
        if ($variation_id > 0) {
            // استخدام الدالة المساعدة لتحديث المخزون
            update_variation_stock($variation_id, $quantity);
        }

        wp_send_json_success(array(
            'message' => 'تم إضافة المنتج للسلة بنجاح',
            'cart_url' => wc_get_cart_url()
        ));
    } else {
        wp_send_json_error('حدث خطأ أثناء إضافة المنتج للسلة');
    }
}

add_action('wp_ajax_add_to_cart_with_custom_data', 'add_to_cart_with_custom_data');
add_action('wp_ajax_nopriv_add_to_cart_with_custom_data', 'add_to_cart_with_custom_data');

// معالجة الطلب المباشر من السلة
function place_direct_order() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        $current_domain = get_current_domain();
        $licensed_domain = get_option('custom_order_form_licensed_domain', '');
        $is_domain_mismatch = !empty($licensed_domain) && $licensed_domain !== $current_domain;

        if ($is_domain_mismatch) {
            wp_send_json_error('هذه الإضافة مرخصة لنطاق آخر (' . esc_html($licensed_domain) . '). يرجى شراء ترخيص جديد لهذا النطاق.');
        } else {
            wp_send_json_error('هذه الإضافة تتطلب ترخيص صالح للعمل.');
        }
        return;
    }

    // إنشاء طلب جديد من السلة الحالية
    $cart = WC()->cart;

    if ($cart->is_empty()) {
        wp_send_json_error('السلة فارغة');
        return;
    }

    try {
        // إنشاء طلب جديد
        $order = wc_create_order();

        // إضافة منتجات السلة إلى الطلب
        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            // إضافة المنتج مع البيانات المخصصة
            $product = wc_get_product($cart_item['product_id']);
            $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
            if ($variation_id) {
                $product = wc_get_product($variation_id);
            }
            $item = new WC_Order_Item_Product();
            $item->set_props(array(
                'product'    => $product,
                'quantity'   => $cart_item['quantity'],
                'subtotal'   => $cart_item['line_subtotal'],
                'total'     => $cart_item['line_total'],
                'variation' => isset($cart_item['variation']) ? $cart_item['variation'] : array(),
            ));
            $order->add_item($item);

            // إضافة البيانات المخصصة للطلب
            if (isset($cart_item['customer_data'])) {
                $customer_data = $cart_item['customer_data'];

                // تقسيم الاسم الكامل
                $name_parts = explode(' ', $customer_data['full_name']);
                $first_name = array_shift($name_parts);
                $last_name = implode(' ', $name_parts);

                // إعداد بيانات العنوان
                $address_data = array(
                    'first_name' => $first_name,
                    'last_name'  => $last_name,
                    'phone'      => $customer_data['phone'],
                    'country'    => 'DZ', // الدولة دائماً الجزائر
                    'state'      => $customer_data['country'], // الولاية (مع الرقم والاسم)
                    'city'       => $customer_data['city'], // البلدية
                    'address_1'  => $customer_data['address'] . ' (' .
                                  ($customer_data['delivery_type'] == 'home' ? 'توصيل للمنزل' : 'توصيل للمكتب') . ')'
                );

                // تعيين عناوين الفواتير والشحن
                $order->set_address($address_data, 'billing');
                $order->set_address($address_data, 'shipping');

                // حفظ البيانات الإضافية
                $order->update_meta_data('_customer_full_name', $customer_data['full_name']);
                $order->update_meta_data('delivery_type', $customer_data['delivery_type']);
            }
        }

        // إضافة رسوم الشحن من السلة
        // إضافة رسوم الشحن لآخر منتج في السلة
        $cart_items = $cart->get_cart();
        $last_cart_item = end($cart_items);

        if (isset($last_cart_item['customer_data']['shipping_cost']) &&
            $last_cart_item['customer_data']['shipping_cost'] > 0) {
            $item = new WC_Order_Item_Shipping();
            $item->set_method_title('رسوم التوصيل');
            $item->set_total($last_cart_item['customer_data']['shipping_cost']);
            $order->add_item($item);
        }

        // حساب المجاميع
        $order->calculate_totals();

        // تحديث المخزون بشكل صريح للمنتجات المتغيرة
        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
            if ($variation_id > 0) {
                // استخدام الدالة المساعدة لتحديث المخزون
                update_variation_stock($variation_id, $cart_item['quantity']);
            }
        }

        // تحديث حالة الطلب وحفظه
        $order->update_status('processing');
        $order->save();

        // تطبيق تحديث المخزون
        wc_maybe_reduce_stock_levels($order->get_id());

        // إفراغ السلة
        WC()->cart->empty_cart();

        wp_send_json_success(array(
            'redirect_url' => $order->get_checkout_order_received_url()
        ));
    } catch (Exception $e) {
        wp_send_json_error($e->getMessage());
    }
}

add_action('wp_ajax_place_direct_order', 'place_direct_order');
add_action('wp_ajax_nopriv_place_direct_order', 'place_direct_order');

// إخفاء زر checkout الافتراضي في صفحة السلة
function remove_checkout_button() {
    if (is_cart()) {
        remove_action('woocommerce_proceed_to_checkout', 'woocommerce_button_proceed_to_checkout', 20);
    }
}
add_action('template_redirect', 'remove_checkout_button');

// عرض البيانات المخصصة في السلة
function display_cart_item_custom_data($item_data, $cart_item) {
    if (isset($cart_item['customer_data'])) {
        $customer_data = $cart_item['customer_data'];

        $item_data[] = array(
            'key' => 'الاسم',
            'value' => $customer_data['full_name']
        );

        $item_data[] = array(
            'key' => 'رقم الهاتف',
            'value' => $customer_data['phone']
        );

        if (!empty($customer_data['address'])) {
            $item_data[] = array(
                'key' => 'العنوان',
                'value' => $customer_data['address']
            );
        }

        $delivery_type = $customer_data['delivery_type'] === 'home' ? 'توصيل للمنزل' : 'توصيل للمكتب';
        $item_data[] = array(
            'key' => 'نوع التوصيل',
            'value' => $delivery_type
        );
    }
    return $item_data;
}
add_filter('woocommerce_get_item_data', 'display_cart_item_custom_data', 10, 2);

// إضافة رسوم الشحن للسلة
function add_cart_custom_shipping_fee() {
    if (is_admin() && !defined('DOING_AJAX')) return;

    $cart = WC()->cart;
    if ($cart->is_empty()) return;

    foreach ($cart->get_cart() as $cart_item) {
        if (isset($cart_item['customer_data']['shipping_cost']) && $cart_item['customer_data']['shipping_cost'] > 0) {
            $cart->add_fee('رسوم التوصيل', $cart_item['customer_data']['shipping_cost']);
            break; // نضيف رسوم التوصيل مرة واحدة فقط
        }
    }
}
add_action('woocommerce_cart_calculate_fees', 'add_cart_custom_shipping_fee');

function add_custom_order_form() {
    // التحقق من صلاحية الترخيص قبل عرض النموذج
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        echo '<div class="custom-order-form-container">';
        echo '<p class="license-error">' . __('هذه الإضافة تتطلب ترخيص صالح للعمل.', 'custom-order-form') . '</p>';
        echo '<p>' . __('يرجى <a href="' . admin_url('plugins.php') . '">تفعيل الترخيص</a> للاستفادة من جميع مميزات الإضافة.', 'custom-order-form') . '</p>';
        echo '</div>';
        return;
    }

    if (is_product()) {
        global $product;

        $has_variations = $product->is_type('variable');
        $variations = [];

        if ($has_variations) {
            $attributes = $product->get_variation_attributes();
            foreach ($attributes as $attribute => $values) {
                $variations[$attribute] = $values;
            }
        }

        echo '<div class="custom-order-form-container">';
        include 'order-form-template.php';
        echo '</div>';

        // إضافة زر الشراء المثبت
        $button_settings = get_option('custom_order_form_button_settings', array(
            'show_sticky_button' => true,
            'button_text' => 'اشتري الآن'
        ));

        if ($button_settings['show_sticky_button']) {
            echo '<div class="sticky-buy-button">';
            echo '<button onclick="scrollToOrderForm()">';
            echo esc_html($button_settings['button_text']);
            echo '</button>';
            echo '</div>';

            // إضافة JavaScript للتمرير
            echo '<script>
                function scrollToOrderForm() {
                    const form = document.querySelector(".custom-order-form-container");
                    if (form) {
                        form.scrollIntoView({ behavior: "smooth" });
                    }
                }

                // إخفاء/إظهار الزر عند التمرير
                window.addEventListener("scroll", function() {
                    const button = document.querySelector(".sticky-buy-button");
                    const form = document.querySelector(".custom-order-form-container");
                    if (button && form) {
                        const formTop = form.getBoundingClientRect().top;
                        const formBottom = form.getBoundingClientRect().bottom;

                        if (formTop > window.innerHeight || formBottom < 0) {
                            button.classList.add("visible");
                        } else {
                            button.classList.remove("visible");
                        }
                    }
                });
            </script>';
        }
    }
}

// تغيير موضع ظهور النموذج إلى مكان الوصف القصير
remove_action('woocommerce_before_single_product_summary', 'add_custom_order_form', 10);
add_action('woocommerce_single_product_summary', 'add_custom_order_form', 20);

function custom_order_form_assets() {
    // تحميل الأصول الأساسية دائماً (مثل Font Awesome) لتجنب ظهور الطبقة البيضاء
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css');

    // التحقق من صلاحية الترخيص قبل تحميل الأصول الخاصة بالإضافة
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        // تحميل CSS أساسي فقط لتجنب ظهور الطبقة البيضاء
        $basic_css = "
            .custom-order-form-container {
                padding: 15px;
                margin: 15px 0;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f8f9fa;
                text-align: center;
            }
            .license-error {
                color: #721c24;
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
            }
        ";
        wp_add_inline_style('font-awesome', $basic_css);
        return;
    }

    // تحميل جميع الأصول إذا كان الترخيص صالحاً
    wp_enqueue_style('custom-order-form-styles', plugin_dir_url(__FILE__) . 'styles.css');
    wp_enqueue_script('custom-order-form-scripts', plugin_dir_url(__FILE__) . 'script.js', array('jquery'), null, true);
    wp_enqueue_script('algeria-cities', plugin_dir_url(__FILE__) . 'algeria-cities.js', array(), null, true);

    // تحميل سكربت السلة في صفحة السلة فقط
    if (is_cart()) {
        wp_enqueue_script('cart-submit', plugin_dir_url(__FILE__) . 'js/cart-submit.js', array('jquery'), null, true);
        wp_localize_script('cart-submit', 'woocommerce_params', array(
            'ajax_url' => admin_url('admin-ajax.php')
        ));
    }

    // Get form settings
    $design_settings = get_option('custom_order_form_design', array(
        'primaryColor' => '#2563eb',
        'buttonColor' => '#2563eb',
        'backgroundColor' => '#ffffff',
        'textColor' => '#1f2937',
        'borderColor' => '#e2e8f0',
        'borderRadius' => '12',
        'fontFamily' => 'IBM Plex Sans Arabic'
    ));

    $field_visibility = get_option('custom_order_form_field_visibility', array(
        'show_address' => true,
        'show_state' => true,
        'show_municipality' => true
    ));

    $shipping_settings = get_option('custom_order_form_shipping_settings', array(
        'fixed_price' => 0,
        'use_fixed_price' => false
    ));

    // Add Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&family=Tajawal:wght@400;500;700&family=Cairo:wght@400;600;700&display=swap', array());

    // Pass settings to JavaScript
    // Get WhatsApp settings
    $whatsapp_settings = get_option('custom_order_form_whatsapp_settings', array(
        'number' => '',
        'enabled' => true
    ));

    // Get spam settings
    $spam_settings = get_option('custom_order_form_spam_settings', array(
        'disable_autocomplete' => false,
        'disable_copy_paste' => false,
        'limit_orders' => false,
        'save_abandoned' => true
    ));

    // الحصول على أسعار التوصيل المخصصة
    $shipping_prices = get_option('custom_order_form_shipping_prices', array());

    wp_localize_script('custom-order-form-scripts', 'woocommerce_params', array(
        'product_id' => get_the_ID(),
        'ajax_url' => admin_url('admin-ajax.php'),
        'form_settings' => array(
            'design' => $design_settings,
            'fieldVisibility' => $field_visibility,
            'shipping' => $shipping_settings,
            'whatsapp_number' => $whatsapp_settings['number'],
            'whatsapp_enabled' => $whatsapp_settings['enabled'],
            'spam_settings' => $spam_settings,
            'shipping_prices' => $shipping_prices
        )
    ));

    // Add inline CSS for custom styling
    $custom_css = "
        :root {
            --primary-color: {$design_settings['primaryColor']};
            --button-color: {$design_settings['buttonColor']};
            --background-color: {$design_settings['backgroundColor']};
            --text-color: {$design_settings['textColor']};
            --border-color: {$design_settings['borderColor']};
            --border-radius: {$design_settings['borderRadius']}px;
            --font-family: {$design_settings['fontFamily']}, sans-serif;
        }

        /* تنسيق قسم حالة المخزون */
        .stock-status-container {
            margin: 15px 0;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            background-color: #f8f9fa;
            border: 1px solid var(--border-color);
        }

        .stock-status-container .in-stock {
            color: #10b981;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stock-status-container .out-of-stock {
            color: #ef4444;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stock-status-container .backorder {
            color: #f59e0b;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* تنسيق الأزرار المعطلة */
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    ";
    wp_add_inline_style('custom-order-form-styles', $custom_css);
}
add_action('wp_enqueue_scripts', 'custom_order_form_assets');

// تعديل رابط صفحة الشكر
function custom_thankyou_page_redirect($order_received_url, $order = null) {
    $custom_thank_you_page = get_option('custom_order_form_thank_you_page', '');
    if (!empty($custom_thank_you_page) && $order) {
        // إنشاء رمز مؤقت فريد
        $token = wp_generate_password(32, false);
        // حفظ الرمز مع الطلب
        $order->update_meta_data('_thank_you_token', $token);
        $order->save();

        // إضافة الرمز ومعرف الطلب للرابط
        return add_query_arg(
            array(
                'order_id' => $order->get_id(),
                'token' => $token
            ),
            $custom_thank_you_page
        );
    }
    return $order_received_url;
}

// إضافة shortcode لعرض تفاصيل الطلب
function display_order_details_shortcode() {
    // التحقق من صلاحية الترخيص
    if (!function_exists('is_license_valid') || !is_license_valid()) {
        return '<p>هذه الإضافة تتطلب ترخيص صالح للعمل.</p>';
    }

    // التحقق من وجود معرف الطلب والرمز
    if (!isset($_GET['order_id']) || !isset($_GET['token'])) {
        return '<p>عذراً، الرابط غير صالح.</p>';
    }

    $order_id = intval($_GET['order_id']);
    $token = sanitize_text_field($_GET['token']);
    $order = wc_get_order($order_id);

    if (!$order) {
        return '<p>عذراً، لم يتم العثور على الطلب.</p>';
    }

    // التحقق من صحة الرمز
    $stored_token = $order->get_meta('_thank_you_token');
    if (empty($stored_token) || $stored_token !== $token) {
        return '<p>عذراً، هذا الرابط غير صالح أو تم استخدامه مسبقاً.</p>';
    }

    // حذف الرمز بعد التحقق منه لمنع استخدام الرابط مرة أخرى
    $order->delete_meta_data('_thank_you_token');
    $order->save();

    // تجميع تفاصيل الطلب
    $output = '<div class="order-details-container">';
    $output .= '<h2>تفاصيل الطلب #' . $order->get_order_number() . '</h2>';

    // معلومات العميل
    $output .= '<div class="customer-details">';
    $output .= '<h3>معلومات العميل</h3>';
    $output .= '<p><strong>الاسم:</strong> ' . $order->get_meta('_customer_full_name') . '</p>';
    $output .= '<p><strong>رقم الهاتف:</strong> ' . $order->get_billing_phone() . '</p>';
    $output .= '<p><strong>العنوان:</strong> ' . $order->get_billing_address_1() . '</p>';
    $output .= '<p><strong>الولاية:</strong> ' . $order->get_billing_state() . '</p>';
    $output .= '<p><strong>البلدية:</strong> ' . $order->get_billing_city() . '</p>';
    $output .= '<p><strong>نوع التوصيل:</strong> ' . ($order->get_meta('delivery_type') === 'home' ? 'توصيل للمنزل' : 'توصيل للمكتب') . '</p>';
    $output .= '</div>';

    // تفاصيل المنتجات
    $output .= '<div class="products-details">';
    $output .= '<h3>المنتجات المطلوبة</h3>';
    $output .= '<table class="order-items">';
    $output .= '<thead><tr><th>المنتج</th><th>الكمية</th><th>السعر</th></tr></thead>';
    $output .= '<tbody>';

    foreach ($order->get_items() as $item) {
        $output .= '<tr>';
        $output .= '<td>' . $item->get_name();
        // إضافة تفاصيل المتغيرات إن وجدت
        if ($item->get_variation_id()) {
            $variations = array();
            foreach ($item->get_meta_data() as $meta) {
                if (strpos($meta->key, 'pa_') !== false) {
                    $variations[] = wc_attribute_label(str_replace('pa_', '', $meta->key)) . ': ' . $meta->value;
                }
            }
            if (!empty($variations)) {
                $output .= ' (' . implode(', ', $variations) . ')';
            }
        }
        $output .= '</td>';
        $output .= '<td>' . $item->get_quantity() . '</td>';
        $output .= '<td>' . wc_price($item->get_total()) . '</td>';
        $output .= '</tr>';
    }
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';

    // تفاصيل الدفع
    $output .= '<div class="payment-details">';
    $output .= '<h3>تفاصيل الدفع</h3>';
    $output .= '<table class="order-totals">';

    // المجموع الفرعي
    $output .= '<tr>';
    $output .= '<td>المجموع الفرعي:</td>';
    $output .= '<td>' . wc_price($order->get_subtotal()) . '</td>';
    $output .= '</tr>';

    // رسوم الشحن
    if ($order->get_shipping_total() > 0) {
        $output .= '<tr>';
        $output .= '<td>رسوم التوصيل:</td>';
        $output .= '<td>' . wc_price($order->get_shipping_total()) . '</td>';
        $output .= '</tr>';
    }

    // المجموع الكلي
    $output .= '<tr class="total">';
    $output .= '<td>المجموع الكلي:</td>';
    $output .= '<td>' . wc_price($order->get_total()) . '</td>';
    $output .= '</tr>';

    $output .= '</table>';
    $output .= '</div>';

    $output .= '</div>';

    return $output;
}

// تسجيل الـ shortcode
add_shortcode('order_details', 'display_order_details_shortcode');

// إضافة CSS مخصص لتفاصيل الطلب
function add_order_details_styles() {
    $custom_css = "
        .order-details-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .order-details-container h2 {
            color: var(--text-color);
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }
        .customer-details, .products-details, .payment-details {
            margin-bottom: 2rem;
        }
        .customer-details h3, .products-details h3, .payment-details h3 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }
        .order-items, .order-totals {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .order-items th, .order-items td,
        .order-totals td {
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            text-align: right;
        }
        .order-items th {
            background: #f9fafb;
            font-weight: 600;
        }
        .order-totals tr.total {
            font-weight: 600;
            font-size: 1.1em;
            border-top: 2px solid var(--border-color);
        }
        @media (max-width: 768px) {
            .order-details-container {
                padding: 1rem;
                margin: 1rem;
            }
        }
    ";
    wp_add_inline_style('custom-order-form-styles', $custom_css);
}
add_action('wp_enqueue_scripts', 'add_order_details_styles', 20);
add_filter('woocommerce_get_checkout_order_received_url', 'custom_thankyou_page_redirect', 10, 2);

// حفظ إعدادات صفحة الشكر
function save_thank_you_page_settings() {
    if (isset($_POST['thank_you_page'])) {
        $thank_you_page = esc_url_raw($_POST['thank_you_page']);
        update_option('custom_order_form_thank_you_page', $thank_you_page);
    }
}
add_action('admin_post_save_thank_you_page', 'save_thank_you_page_settings');

// إنشاء جدول الطلبات المتروكة
function create_abandoned_orders_table() {
    // لا نحتاج لإنشاء جدول في قاعدة البيانات لأننا نستخدم خيارات ووردبريس
    // لكن يمكننا استخدام هذه الدالة لتهيئة الخيارات الأولية

    // تحديث النطاق المرخص عند تنشيط الإضافة
    if (function_exists('sf_fs') && sf_fs()->is_paying()) {
        update_option('custom_order_form_licensed_domain', get_current_domain());
    }
}

// تنفيذ عند تنشيط الإضافة
register_activation_hook(__FILE__, 'create_abandoned_orders_table');
