<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<?php
$field_visibility = get_option('custom_order_form_field_visibility', array(
    'show_address' => true,
    'show_state' => true,
    'show_municipality' => true
));

$whatsapp_settings = get_option('custom_order_form_whatsapp_settings', array(
    'number' => '',
    'enabled' => true
));

$shipping_settings = get_option('custom_order_form_shipping_settings', array(
    'fixed_price' => 0,
    'use_fixed_price' => false
));

$form_settings = array(
    'fieldVisibility' => $field_visibility,
    'whatsapp_number' => $whatsapp_settings['number'],
    'whatsapp_enabled' => $whatsapp_settings['enabled'],
    'shipping' => $shipping_settings
);

// التحقق من حالة المخزون للمنتج
$stock_status = '';
$is_in_stock = false;
$stock_quantity = 0;
$backorders_allowed = false;

// التحقق من إعدادات WooCommerce للسماح بالطلب في حالة نفاذ المخزون
$allow_backorders = get_option('woocommerce_manage_stock') === 'yes' &&
                    get_option('woocommerce_backorders_allowed') === 'yes';

if ($product->is_type('variable')) {
    // للمنتجات المتغيرة، نعرض حالة المخزون الافتراضية ثم نحدثها عند اختيار المتغير
    $is_in_stock = $product->is_in_stock();
    $backorders_allowed = $product->backorders_allowed();

    // التحقق من إمكانية الطلب بناءً على إعدادات المخزون
    $can_order = $is_in_stock || $backorders_allowed || $allow_backorders;

    // الحصول على كمية المخزون
    $stock_quantity = $product->get_stock_quantity();
    $stock_quantity_text = $stock_quantity !== null ? ' - ' . $stock_quantity . ' قطعة متوفرة' : '';

    if ($is_in_stock) {
        $stock_status = '<span class="in-stock"><i class="fas fa-check-circle"></i> متوفر في المخزون' . $stock_quantity_text . '</span>';
    } else {
        if ($backorders_allowed || $allow_backorders) {
            $stock_status = '<span class="backorder"><i class="fas fa-clock"></i> متوفر للطلب المسبق</span>';
        } else {
            $stock_status = '<span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر في المخزون</span>';
        }
    }
} else {
    // للمنتجات البسيطة، نعرض حالة المخزون مباشرة
    $is_in_stock = $product->is_in_stock();
    $backorders_allowed = $product->backorders_allowed();

    // التحقق من إمكانية الطلب بناءً على إعدادات المخزون
    $can_order = $is_in_stock || $backorders_allowed || $allow_backorders;

    // الحصول على كمية المخزون
    $stock_quantity = $product->get_stock_quantity();
    $stock_quantity_text = $stock_quantity !== null ? ' - ' . $stock_quantity . ' قطعة متوفرة' : '';

    if ($is_in_stock) {
        $stock_status = '<span class="in-stock"><i class="fas fa-check-circle"></i> متوفر في المخزون' . $stock_quantity_text . '</span>';
    } else {
        if ($backorders_allowed || $allow_backorders) {
            $stock_status = '<span class="backorder"><i class="fas fa-clock"></i> متوفر للطلب المسبق</span>';
        } else {
            $stock_status = '<span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر في المخزون</span>';
        }
    }
}
?>
<?php
$spam_settings = get_option('custom_order_form_spam_settings', array(
    'disable_autocomplete' => false,
    'disable_copy_paste' => false,
    'limit_orders' => false,
    'save_abandoned' => true
));

// التحقق من حد الطلبات اليومي
$can_order = true;
$error_message = '';
if ($spam_settings['limit_orders']) {
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $last_order = get_transient('last_order_' . $user_ip);
    if ($last_order) {
        $can_order = false;
        $time_left = human_time_diff(time(), $last_order + (24 * HOUR_IN_SECONDS));
        $error_message = sprintf('عذراً، يمكنك إرسال طلب جديد بعد %s', $time_left);
    }
}
?>
<div class="custom-order-form-container">
    <?php if (!$can_order): ?>
    <div class="order-limit-message">
        <?php echo esc_html($error_message); ?>
    </div>
    <?php endif; ?>

    <form id="orderForm" class="custom-order-form needs-validation"
          <?php if ($spam_settings['disable_autocomplete']): ?>
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
          <?php endif; ?>
          novalidate>
        <?php if ($spam_settings['disable_autocomplete']): ?>
        <!-- Trick browsers into disabling autofill -->
        <input type="text" style="display:none" name="fakeusernameremembered"/>
        <input type="password" style="display:none" name="fakepasswordremembered"/>
        <?php endif; ?>
        <h2>
            <i class="fas fa-shopping-cart"></i>
            <?php echo esc_html(get_option('custom_order_form_title', 'أضف معلوماتك في الأسفل لطلب هذا المنتج')); ?>
        </h2>

        <div class="form-group">
            <div class="input-group">
                <input type="text"
                       id="fullName"
                       name="fullName"
                       class="form-control"
                       placeholder="الاسم بالكامل"
                       <?php if ($spam_settings['disable_autocomplete']): ?>
                       autocomplete="off"
                       autocorrect="off"
                       autocapitalize="off"
                       data-form-type="other"
                       data-lpignore="true"
                       readonly
                       onfocus="this.removeAttribute('readonly');"
                       <?php endif; ?>
                       required>
                <span class="input-group-text">
                    <i class="fas fa-user"></i>
                </span>
            </div>
        </div>

        <div class="form-group">
            <div class="input-group">
                <input type="tel"
                       id="phone"
                       name="phone"
                       class="form-control"
                       placeholder="رقم الهاتف"
                       <?php if ($spam_settings['disable_autocomplete']): ?>
                       autocomplete="off"
                       autocorrect="off"
                       autocapitalize="off"
                       data-form-type="other"
                       data-lpignore="true"
                       readonly
                       onfocus="this.removeAttribute('readonly');"
                       <?php endif; ?>
                       required>
                <span class="input-group-text">
                    <i class="fas fa-phone-alt"></i>
                </span>
            </div>
        </div>

        <?php if (isset($field_visibility['show_state']) && $field_visibility['show_state']): ?>
        <div class="form-group">
            <div class="input-group">
                <select id="country"
                        name="country"
                        class="form-select"
                        <?php if ($spam_settings['disable_autocomplete']): ?>
                        autocomplete="off"
                        data-form-type="other"
                        data-lpignore="true"
                        <?php endif; ?>
                        required>
                    <option value="">اختر الولاية</option>
                </select>
                <span class="input-group-text">
                    <i class="fas fa-map-marker-alt"></i>
                </span>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($field_visibility['show_municipality']) && $field_visibility['show_municipality']): ?>
        <div class="form-group">
            <div class="input-group">
                <select id="city"
                        name="city"
                        class="form-select"
                        <?php if ($spam_settings['disable_autocomplete']): ?>
                        autocomplete="off"
                        data-form-type="other"
                        data-lpignore="true"
                        <?php endif; ?>
                        required>
                    <option value="">اختر البلدية</option>
                </select>
                <span class="input-group-text">
                    <i class="fas fa-city"></i>
                </span>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($has_variations): ?>
    <div class="variations-group">
        <label>خيارات المنتج:</label>
        <?php foreach ($variations as $attribute_name => $options):
            $attribute_id = 'attribute_' . sanitize_title($attribute_name);
            $attribute_type = '';

            // تحديد نوع السمة (لون، حجم، صورة)
            if (strpos(strtolower($attribute_name), 'color') !== false ||
                strpos(strtolower($attribute_name), 'colour') !== false ||
                strpos(strtolower($attribute_name), 'لون') !== false) {
                $attribute_type = 'color';
            } elseif (strpos(strtolower($attribute_name), 'size') !== false ||
                     strpos(strtolower($attribute_name), 'مقاس') !== false ||
                     strpos(strtolower($attribute_name), 'حجم') !== false) {
                $attribute_type = 'size';
            } elseif (
                // تحقق من وجود صور مخصصة للمتغيرات
                ($variation_images = get_post_meta($product->get_id(), '_variation_images', true)) &&
                isset($variation_images[$attribute_name]) &&
                !empty(array_filter($variation_images[$attribute_name]))
            ) {
                $attribute_type = 'image';
            } else {
                $attribute_type = 'text';
            }
        ?>
        <div class="form-group variation-group">
            <label><?php echo wc_attribute_label($attribute_name); ?></label>
            <div class="variation-options">
                <input type="hidden" name="<?php echo esc_attr($attribute_id); ?>" class="variation-select" required>
                    <?php foreach ($options as $option):
                        $option_id = $attribute_id . '_' . sanitize_title($option);

                        // البحث عن صورة مخصصة من الـ metabox
                        $variation_images = get_post_meta($product->get_id(), '_variation_images', true);
                        $custom_image_id = isset($variation_images[$attribute_name][$option]) ? $variation_images[$attribute_name][$option] : '';
                        $custom_image_url = $custom_image_id ? wp_get_attachment_image_url($custom_image_id, 'thumbnail') : '';

                        // إذا لم تكن هناك صورة مخصصة، نبحث عن صورة المتغير
                        if (!$custom_image_url) {
                            // البحث عن المتغيرات المتاحة
                            $available_variations = $product->get_available_variations();
                            foreach ($available_variations as $variation) {
                                $variation_attributes = $variation['attributes'];
                                $attribute_key = 'attribute_' . sanitize_title($attribute_name);

                                // إذا كان هذا المتغير يطابق الخيار الحالي
                                if (isset($variation_attributes[$attribute_key]) &&
                                    $variation_attributes[$attribute_key] === $option) {
                                    $custom_image_url = $variation['image']['thumb_src'];
                                    break;
                                }
                            }
                        }

                        switch ($attribute_type):
                            case 'color':
                                $color = $option;
                                $color_map = get_option('custom_order_form_colors', array(
                                    'أحمر' => '#ff0000',
                                    'أخضر' => '#00ff00',
                                    'أزرق' => '#0000ff',
                                    'أسود' => '#000000',
                                    'أبيض' => '#ffffff',
                                    'أصفر' => '#ffff00',
                                    'برتقالي' => '#ffa500',
                                    'بني' => '#a52a2a',
                                    'رمادي' => '#808080',
                                    'ذهبي' => '#ffd700',
                                    'فضي' => '#c0c0c0',
                                    'وردي' => '#ffc0cb',
                                    'بنفسجي' => '#800080'
                                ));
                                if (isset($color_map[$option])) {
                                    $color = $color_map[$option];
                                }
                                ?>
                                <div class="swatch-option color-swatch"
                                     data-value="<?php echo esc_attr($option); ?>"
                                     data-attribute="<?php echo esc_attr($attribute_id); ?>"
                                     style="background-color: <?php echo esc_attr($color); ?>">
                                    <span class="swatch-label"><?php echo esc_html($option); ?></span>
                                </div>
                                <?php
                                break;

                            case 'size':
                                ?>
                                <div class="swatch-option size-swatch"
                                     data-value="<?php echo esc_attr($option); ?>"
                                     data-attribute="<?php echo esc_attr($attribute_id); ?>">
                                    <?php echo esc_html($option); ?>
                                </div>
                                <?php
                                break;

                            case 'image':
                                if ($custom_image_url): ?>
                                <div class="swatch-option image-swatch"
                                     data-value="<?php echo esc_attr($option); ?>"
                                     data-attribute="<?php echo esc_attr($attribute_id); ?>"
                                     data-image-url="<?php echo esc_url($custom_image_url); ?>"
                                     title="<?php echo esc_attr($option); ?>">
                                    <img src="<?php echo esc_url($custom_image_url); ?>"
                                         alt="<?php echo esc_attr($option); ?>">
                                    <span class="swatch-label"><?php echo esc_html($option); ?></span>
                                </div>
                                <?php else: ?>
                                <div class="swatch-option text-swatch"
                                     data-value="<?php echo esc_attr($option); ?>"
                                     data-attribute="<?php echo esc_attr($attribute_id); ?>">
                                    <?php echo esc_html($option); ?>
                                </div>
                                <?php endif;
                                break;

                            default:
                                ?>
                                <div class="swatch-option text-swatch"
                                     data-value="<?php echo esc_attr($option); ?>"
                                     data-attribute="<?php echo esc_attr($attribute_id); ?>">
                                    <?php echo esc_html($option); ?>
                                </div>
                                <?php
                        endswitch;
                    endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div class="form-group" id="addressGroup" style="<?php echo (!isset($field_visibility['show_address']) || !$field_visibility['show_address']) ? 'display: none;' : ''; ?>">
            <div class="input-group">
                <input type="text"
                       id="address"
                       name="address"
                       class="form-control"
                       placeholder="العنوان بالتفصيل"
                       <?php if ($spam_settings['disable_autocomplete']): ?>
                       autocomplete="off"
                       autocorrect="off"
                       autocapitalize="off"
                       data-form-type="other"
                       data-lpignore="true"
                       readonly
                       onfocus="this.removeAttribute('readonly');"
                       <?php endif; ?>
                       required>
                <span class="input-group-text">
                    <i class="fas fa-home"></i>
                </span>
            </div>
        </div>

        <div class="delivery-type-group">
            <label>نوع التوصيل:</label>
            <div class="btn-group">
                <input type="radio" class="btn-check" name="delivery_type" id="home_delivery" value="home" checked required>
                <label class="btn" for="home_delivery">
                    <i class="fas fa-home"></i>
                    التوصيل للمنزل
                </label>

                <input type="radio" class="btn-check" name="delivery_type" id="office_delivery" value="office" required>
                <label class="btn" for="office_delivery">
                    <i class="fas fa-building"></i>
                    التوصيل للمكتب
                </label>
            </div>
        </div>

        <div class="d-flex align-items-center gap-3">
            <div class="custom-quantity-control">
                <button type="button" id="decreaseQuantity">
                    <i class="fas fa-minus"></i>
                </button>
                <input type="number" id="quantity" name="quantity" value="1" min="1">
                <button type="button" id="increaseQuantity">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <button id="confirmOrder" type="submit" class="btn btn-primary flex-grow-1" <?php echo !$can_order ? 'disabled' : ''; ?>>
                <span id="confirmOrderText">تأكيد الطلب</span>
                <span id="confirmOrderLoading" class="d-none">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري التأكيد...
                </span>
            </button>
        </div>

        <div class="d-flex gap-2 mt-3">
            <?php if ($form_settings['whatsapp_enabled']): ?>
                <button type="button" class="btn btn-success flex-grow-1" onclick="orderViaWhatsApp()" <?php echo !$can_order ? 'disabled' : ''; ?>>
                    <i class="fab fa-whatsapp"></i>
                    طلب عبر الواتساب
                </button>
            <?php endif; ?>

            <?php
            // التحقق من إعداد زر السلة للمنتج
            $show_cart = get_post_meta(get_the_ID(), '_show_cart_button', true);
            if ($show_cart === 'yes'):
            ?>
                <button type="button" class="btn btn-primary flex-grow-1 add-to-cart-btn" data-product-id="<?php echo get_the_ID(); ?>" <?php echo !$can_order ? 'disabled' : ''; ?>>
                    <i class="fas fa-cart-plus"></i>
                    إضافة إلى السلة
                </button>
            <?php endif; ?>
        </div>

        <!-- إضافة قسم حالة المخزون -->
        <div class="stock-status-container">
            <div id="stockStatus" data-stock="<?php echo $can_order ? 'true' : 'false'; ?>" data-backorders="<?php echo $backorders_allowed ? 'true' : 'false'; ?>">
                <?php echo $stock_status; ?>
            </div>
        </div>

        <div class="order-summary">
            <h3 id="toggleSummary">
                ملخص الطلب
                <i class="fas fa-chevron-down"></i>
            </h3>
            <div id="summaryContent">
                <p>
                    <span><i class="fas fa-box"></i> <?php echo get_the_title(); ?></span>
                </p>
                <p>
                    <span><i class="fas fa-tag"></i> سعر المنتج: </span>
                    <?php
                    if ($product->is_type('variable')) {
                        echo '<span>' . $product->get_variation_price('min') . ' د.ج</span>';
                    } else {
                        // التحقق مما إذا كان المنتج البسيط يحتوي على تخفيض
                        $regular_price = $product->get_regular_price();
                        $sale_price = $product->get_sale_price();

                        if ($sale_price && $regular_price > $sale_price) {
                            // عرض السعر القديم مشطوب بجانب السعر الجديد
                            echo '<span class="price-inline-wrapper">';
                            echo '<span class="old-price">' . $regular_price . ' د.ج</span>';
                            echo '<span>' . $sale_price . ' د.ج</span>';
                            echo '</span>';
                        } else {
                            echo '<span>' . $product->get_price() . ' د.ج</span>';
                        }
                    }
                    ?>
                </p>
                <p>
                    <span><i class="fas fa-truck"></i> سعر التوصيل: </span>
                    <span id="shippingPrice">0 د.ج</span>
                </p>
                <p>
                    <span><i class="fas fa-calculator"></i> السعر الإجمالي: </span>
                    <span id="totalPrice">0 د.ج</span>
                </p>
            </div>
        </div>

        <?php
        // تحديد السعر الصحيح للمنتج
        $current_price = $product->get_price();
        $regular_price = $product->get_regular_price();
        $has_discount = false;

        if (!$product->is_type('variable') && $product->is_on_sale()) {
            $has_discount = true;
        }
        ?>
        <input type="hidden" id="basePrice" value="<?php echo $product->is_type('variable') ? $product->get_variation_price('min') : $current_price; ?>">
        <input type="hidden" id="variableProduct" value="<?php echo $product->is_type('variable') ? '1' : '0'; ?>">
        <input type="hidden" id="hasVariations" value="<?php echo $has_variations ? '1' : '0'; ?>">
        <input type="hidden" id="productName" value="<?php echo esc_attr(get_the_title()); ?>">
        <input type="hidden" id="productPrice" name="product_price" value="<?php echo $product->is_type('variable') ? $product->get_variation_price('min') : $current_price; ?>">
        <?php if ($has_discount): ?>
        <input type="hidden" id="regularPrice" value="<?php echo $regular_price; ?>">
        <input type="hidden" id="hasDiscount" value="1">
        <?php endif; ?>
    </form>
</div>
