jQuery(document).ready(function($) {
    // إدارة الألوان في الإعدادات
    const colorTable = $('.color-settings-table tbody');

    // إضافة لون جديد
    $('.add-new-color').on('click', function() {
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="colors[names][]" class="regular-text color-name" value="">
                </td>
                <td>
                    <input type="color" name="colors[values][]" class="color-picker" value="#000000">
                </td>
                <td>
                    <div class="color-preview" style="background-color: #000000"></div>
                    <button type="button" class="button delete-color">حذف</button>
                </td>
            </tr>
        `;
        colorTable.append(newRow);
    });

    // حذف لون
    $(document).on('click', '.delete-color', function() {
        $(this).closest('tr').remove();
    });

    // تحديث المعاينة عند تغيير اللون
    $(document).on('input', 'input[type="color"]', function() {
        $(this).closest('tr').find('.color-preview').css('background-color', $(this).val());
    });

    // تهيئة منتقي الألوان
    $('.color-picker').wpColorPicker();

    // تحديث إعدادات الشحن
    $('input[name="field_visibility[show_state]"]').on('change', function() {
        const fixedShippingField = $('.fixed-shipping-price');
        if (!$(this).is(':checked')) {
            fixedShippingField.slideDown();
        } else {
            fixedShippingField.slideUp();
        }
    });

    // تحديث الإعدادات
    $('#custom-order-form-settings').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        formData.append('action', 'save_form_settings');
        formData.append('nonce', customOrderFormAdmin.nonce);

        const submitButton = $(this).find('button[type="submit"]');
        submitButton.prop('disabled', true);

        $.ajax({
            url: customOrderFormAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showMessage('success', response.data.message);
                }
            },
            error: function() {
                showMessage('error', 'حدث خطأ أثناء حفظ الإعدادات');
            },
            complete: function() {
                submitButton.prop('disabled', false);
            }
        });
    });

    // حذف الطلب المتروك
    $('.delete-abandoned-order').on('click', function() {
        const button = $(this);
        const orderId = button.data('id');

        if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
            $.ajax({
                url: customOrderFormAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'delete_abandoned_order',
                    nonce: customOrderFormAdmin.nonce,
                    order_id: orderId
                },
                success: function(response) {
                    if (response.success) {
                        button.closest('tr').fadeOut(function() {
                            $(this).remove();
                            if ($('.abandoned-orders tbody tr').length === 0) {
                                $('.abandoned-orders table').replaceWith('<p>لا توجد طلبات متروكة حالياً.</p>');
                            }
                        });
                        showMessage('success', 'تم حذف الطلب بنجاح');
                    }
                },
                error: function() {
                    showMessage('error', 'حدث خطأ أثناء حذف الطلب');
                }
            });
        }
    });

    // إضافة حظر جديد
    $('.add-block-button').on('click', function() {
        const type = $('#blockType').val();
        const value = $('#blockValue').val().trim();
        const reason = $('#blockReason').val().trim();

        if (!value) {
            showMessage('error', 'يرجى إدخال قيمة للحظر');
            return;
        }

        // التحقق من صحة القيمة
        if (type === 'ip' && !isValidIP(value)) {
            showMessage('error', 'عنوان IP غير صالح');
            return;
        } else if (type === 'phone' && !isValidPhone(value)) {
            showMessage('error', 'رقم الهاتف غير صالح');
            return;
        }

        $.ajax({
            url: customOrderFormAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'add_block_item',
                nonce: customOrderFormAdmin.nonce,
                type: type,
                value: value,
                reason: reason
            },
            success: function(response) {
                if (response.success) {
                    location.reload(); // تحديث الصفحة لعرض العنصر المحظور الجديد
                }
            },
            error: function() {
                showMessage('error', 'حدث خطأ أثناء إضافة الحظر');
            }
        });
    });

    // إلغاء الحظر
    $('.remove-block-button').on('click', function() {
        const button = $(this);
        const type = button.data('type');
        const value = button.data('value');

        if (confirm('هل أنت متأكد من إلغاء هذا الحظر؟')) {
            $.ajax({
                url: customOrderFormAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'remove_block_item',
                    nonce: customOrderFormAdmin.nonce,
                    type: type,
                    value: value
                },
                success: function(response) {
                    if (response.success) {
                        button.closest('tr').fadeOut(function() {
                            $(this).remove();
                            if ($('.blocked-list tbody tr').length === 0) {
                                $('.blocked-list table').replaceWith('<p>لا توجد عناصر محظورة حالياً.</p>');
                            }
                        });
                        showMessage('success', 'تم إلغاء الحظر بنجاح');
                    }
                },
                error: function() {
                    showMessage('error', 'حدث خطأ أثناء إلغاء الحظر');
                }
            });
        }
    });

    // التحقق من صحة عنوان IP
    function isValidIP(ip) {
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (!ipRegex.test(ip)) return false;
        const parts = ip.split('.');
        return parts.every(part => parseInt(part) >= 0 && parseInt(part) <= 255);
    }

    // التحقق من صحة رقم الهاتف
    function isValidPhone(phone) {
        // يمكن تعديل هذا النمط حسب تنسيق أرقام الهواتف المطلوب
        return /^\d{8,15}$/.test(phone.replace(/[\s\-\+]/g, ''));
    }

    // تبديل علامات التبويب
    $('.settings-tab').on('click', function() {
        const tabId = $(this).data('tab');

        $('.settings-tab').removeClass('active');
        $(this).addClass('active');

        $('.settings-panel').removeClass('active');
        $('#' + tabId + '-panel').addClass('active');

        // تحديث URL مع التبويب النشط
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('tab', tabId);
        window.history.replaceState({}, '', `${window.location.pathname}?${urlParams}`);
    });

    // تحريك زر الحفظ إلى الأسفل عند التمرير
    const saveButton = $('.button.button-primary');
    const originalPosition = saveButton.offset()?.top;

    if (originalPosition) {
        $(window).on('scroll', function() {
            const scrollPosition = $(window).scrollTop();
            if (scrollPosition > originalPosition) {
                saveButton.addClass('sticky-save');
            } else {
                saveButton.removeClass('sticky-save');
            }
        });
    }

    // عرض رسائل النجاح والخطأ
    function showMessage(type, message) {
        const notice = $(`<div class="orders-message ${type}"><p>${message}</p></div>`);
        $('.wrap.custom-order-form-settings h1').after(notice);

        setTimeout(function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        }, 3000);
    }

    // تحديد التبويب النشط من URL
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('tab');
    if (activeTab) {
        $(`.settings-tab[data-tab="${activeTab}"]`).click();
    }

    // معالجة خيارات تعطيل التوصيل
    $('.disable-home-shipping').on('change', function() {
        const state = $(this).data('state');
        const priceInput = $(`input[name="shipping_prices[${state}][home]"]`);

        if ($(this).is(':checked')) {
            // حفظ القيمة السابقة كسمة مخصصة
            priceInput.attr('data-previous-value', priceInput.val());
            priceInput.val(0);
            priceInput.prop('disabled', true);
        } else {
            // استعادة القيمة السابقة إذا كانت موجودة
            const previousValue = priceInput.attr('data-previous-value') || 0;
            priceInput.val(previousValue);
            priceInput.prop('disabled', false);
        }
    });

    $('.disable-office-shipping').on('change', function() {
        const state = $(this).data('state');
        const priceInput = $(`input[name="shipping_prices[${state}][office]"]`);

        if ($(this).is(':checked')) {
            // حفظ القيمة السابقة كسمة مخصصة
            priceInput.attr('data-previous-value', priceInput.val());
            priceInput.val(0);
            priceInput.prop('disabled', true);
        } else {
            // استعادة القيمة السابقة إذا كانت موجودة
            const previousValue = priceInput.attr('data-previous-value') || 0;
            priceInput.val(previousValue);
            priceInput.prop('disabled', false);
        }
    });

    // تهيئة حالة حقول الإدخال عند تحميل الصفحة
    $('.disable-home-shipping, .disable-office-shipping').each(function() {
        if ($(this).is(':checked')) {
            const state = $(this).data('state');
            const type = $(this).hasClass('disable-home-shipping') ? 'home' : 'office';
            const priceInput = $(`input[name="shipping_prices[${state}][${type}]"]`);
            priceInput.prop('disabled', true);
        }
    });

    // معالجة رفع ملف CSV
    $('.upload-csv-button').on('click', function() {
        $('#csv-upload').click();
    });

    $('#csv-upload').on('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // التحقق من نوع الملف
        if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
            showCsvResult('error', 'يرجى اختيار ملف CSV صالح');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(event) {
            processCsvFile(event.target.result);
        };
        reader.onerror = function() {
            showCsvResult('error', 'حدث خطأ أثناء قراءة الملف');
        };
        reader.readAsText(file);
    });

    // معالجة ملف CSV
    function processCsvFile(csvContent) {
        try {
            // تقسيم المحتوى إلى أسطر
            const lines = csvContent.split(/\r\n|\n/);
            if (lines.length < 2) {
                showCsvResult('error', 'الملف فارغ أو لا يحتوي على بيانات كافية');
                return;
            }

            // التحقق من العنوان (الصف الأول)
            const header = lines[0].split(',');
            if (header.length < 3) {
                showCsvResult('error', 'تنسيق الملف غير صحيح. يجب أن يحتوي على ثلاثة أعمدة: الولاية، سعر التوصيل للمنزل، سعر التوصيل للمكتب');
                return;
            }

            let updatedCount = 0;
            let errors = [];

            // معالجة كل سطر (بدءًا من السطر الثاني)
            for (let i = 1; i < lines.length; i++) {
                if (!lines[i].trim()) continue; // تخطي الأسطر الفارغة

                const values = lines[i].split(',');
                if (values.length < 3) {
                    errors.push(`السطر ${i+1}: عدد الأعمدة غير كافٍ`);
                    continue;
                }

                const state = values[0].trim();
                const homePrice = parseInt(values[1].trim());
                const officePrice = parseInt(values[2].trim());

                // التحقق من صحة البيانات
                if (isNaN(homePrice) || isNaN(officePrice) || homePrice < 0 || officePrice < 0) {
                    errors.push(`السطر ${i+1}: قيم الأسعار غير صالحة`);
                    continue;
                }

                // البحث عن حقول الإدخال المناسبة وتحديث قيمها
                const homeInput = $(`input[name="shipping_prices[${state}][home]"]`);
                const officeInput = $(`input[name="shipping_prices[${state}][office]"]`);

                if (homeInput.length && officeInput.length) {
                    homeInput.val(homePrice);
                    officeInput.val(officePrice);

                    // تحديث حالة خانات الاختيار
                    $(`.disable-home-shipping[data-state="${state}"]`).prop('checked', homePrice === 0);
                    $(`.disable-office-shipping[data-state="${state}"]`).prop('checked', officePrice === 0);

                    // تعطيل حقول الإدخال إذا كانت القيمة 0
                    homeInput.prop('disabled', homePrice === 0);
                    officeInput.prop('disabled', officePrice === 0);

                    updatedCount++;
                } else {
                    errors.push(`السطر ${i+1}: لم يتم العثور على الولاية "${state}"`);
                }
            }

            // عرض نتيجة المعالجة
            if (errors.length > 0) {
                showCsvResult('error', `تم تحديث ${updatedCount} ولاية، ولكن هناك ${errors.length} أخطاء:<br>` + errors.join('<br>'));
            } else {
                showCsvResult('success', `تم تحديث أسعار التوصيل لـ ${updatedCount} ولاية بنجاح`);
            }

        } catch (error) {
            console.error('Error processing CSV:', error);
            showCsvResult('error', 'حدث خطأ أثناء معالجة الملف: ' + error.message);
        }
    }

    // عرض نتيجة معالجة ملف CSV
    function showCsvResult(type, message) {
        const resultElement = $('#csv-upload-result');
        resultElement.removeClass('success error').addClass(type);
        resultElement.html(message);

        // إخفاء الرسالة بعد 10 ثوانٍ
        setTimeout(() => {
            resultElement.fadeOut(500, function() {
                $(this).html('').removeClass('success error').show();
            });
        }, 10000);
    }

    // تنزيل نموذج CSV
    $('.download-csv-template').on('click', function() {
        // استخدام وظيفة تنزيل CSV من الخادم
        const downloadUrl = customOrderFormAdmin.ajaxUrl + '?action=download_shipping_csv&nonce=' + customOrderFormAdmin.nonce;

        // فتح نافذة جديدة لتنزيل الملف
        window.open(downloadUrl, '_blank');
    });

    // إعادة تعيين أسعار التوصيل إلى القيم الافتراضية
    $('.reset-shipping-prices').on('click', function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع أسعار التوصيل إلى القيم الافتراضية؟')) {
            // القيم الافتراضية لأسعار التوصيل
            const defaultPrices = {
                "01: ولاية أدرار": { home: 1300, office: 900 },
                "02: ولاية الشلف": { home: 800, office: 500 },
                "03: ولاية الأغواط": { home: 950, office: 600 },
                "04: ولاية أم البواقي": { home: 800, office: 500 },
                "05: ولاية باتنة": { home: 800, office: 500 },
                "06: ولاية بجاية": { home: 800, office: 500 },
                "07: ولاية بسكرة": { home: 950, office: 700 },
                "08: ولاية بشار": { home: 1250, office: 700 },
                "09: ولاية البليدة": { home: 600, office: 400 },
                "10: ولاية البويرة": { home: 750, office: 500 },
                "11: ولاية تمنراست": { home: 1500, office: 1050 },
                "12: ولاية تبسة": { home: 900, office: 500 },
                "13: ولاية تلمسان": { home: 850, office: 500 },
                "14: ولاية تيارت": { home: 800, office: 500 },
                "15: ولاية تيزي وزو": { home: 850, office: 500 },
                "16: ولاية الجزائر": { home: 650, office: 350 },
                "17: ولاية الجلفة": { home: 850, office: 500 },
                "18: ولاية جيجل": { home: 800, office: 500 },
                "19: ولاية سطيف": { home: 850, office: 500 },
                "20: ولاية سعيدة": { home: 850, office: 500 },
                "21: ولاية سكيكدة": { home: 850, office: 500 },
                "22: ولاية سيدي بلعباس": { home: 850, office: 500 },
                "23: ولاية عنابة": { home: 850, office: 500 },
                "24: ولاية قالمة": { home: 850, office: 500 },
                "25: ولاية قسنطينة": { home: 850, office: 500 },
                "26: ولاية المدية": { home: 500, office: 300 },
                "27: ولاية مستغانم": { home: 850, office: 500 },
                "28: ولاية المسيلة": { home: 800, office: 500 },
                "29: ولاية معسكر": { home: 800, office: 500 },
                "30: ولاية ورقلة": { home: 1000, office: 600 },
                "31: ولاية وهران": { home: 800, office: 500 },
                "32: ولاية البيض": { home: 1000, office: 600 },
                "33: ولاية إليزي": { home: 2000, office: 1500 },
                "34: ولاية برج بوعريريج": { home: 800, office: 500 },
                "35: ولاية بومرداس": { home: 800, office: 500 },
                "36: ولاية الطارف": { home: 850, office: 500 },
                "37: ولاية تندوف": { home: 2000, office: 1800 },
                "38: ولاية تيسمسيلت": { home: 750, office: 750 },
                "39: ولاية الوادي": { home: 950, office: 600 },
                "40: ولاية خنشلة": { home: 850, office: 850 },
                "41: ولاية سوق أهراس": { home: 850, office: 500 },
                "42: ولاية تيبازة": { home: 800, office: 500 },
                "43: ولاية ميلة": { home: 850, office: 500 },
                "44: ولاية عين الدفلى": { home: 850, office: 500 },
                "45: ولاية النعامة": { home: 1200, office: 600 },
                "46: ولاية عين تموشنت": { home: 850, office: 500 },
                "47: ولاية غرداية": { home: 950, office: 600 },
                "48: ولاية غليزان": { home: 850, office: 500 },
                "49: ولاية تيميمون": { home: 1400, office: 1400 },
                "50: ولاية برج باجي مختار": { home: 2000, office: 2000 },
                "51: ولاية أولاد جلال": { home: 950, office: 600 },
                "52: ولاية بني عباس": { home: 1400, office: 1400 },
                "53: ولاية عين صالح": { home: 1600, office: 1600 },
                "54: ولاية عين قزام": { home: 1600, office: 1600 },
                "55: ولاية تقرت": { home: 950, office: 600 },
                "56: ولاية جانت": { home: 2000, office: 2000 },
                "57: ولاية المغير": { home: 950, office: 950 },
                "58: ولاية المنيعة": { home: 1000, office: 1000 }
            };

            // تحديث قيم الحقول وإعادة تفعيلها
            for (const state in defaultPrices) {
                // إعادة تعيين قيم التوصيل للمنزل
                $(`input[name="shipping_prices[${state}][home]"]`)
                    .val(defaultPrices[state].home)
                    .prop('disabled', false)
                    .removeAttr('data-previous-value');
                $(`.disable-home-shipping[data-state="${state}"]`).prop('checked', false);

                // إعادة تعيين قيم التوصيل للمكتب
                $(`input[name="shipping_prices[${state}][office]"]`)
                    .val(defaultPrices[state].office)
                    .prop('disabled', false)
                    .removeAttr('data-previous-value');
                $(`.disable-office-shipping[data-state="${state}"]`).prop('checked', false);
            }

            showMessage('success', 'تم إعادة تعيين أسعار التوصيل إلى القيم الافتراضية');
        }
    });
});
