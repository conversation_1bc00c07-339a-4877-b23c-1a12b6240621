<?php
class Custom_Order_Form_Field_Settings {
    public static function render_settings() {
        ?>
        <div class="field-settings-panel p-6 bg-white rounded-lg shadow-sm">
            <h3 class="text-xl font-semibold mb-4">إعدادات الحقول</h3>
            
            <div class="grid gap-4">
                <?php self::render_field_toggles(); ?>
                <?php self::render_custom_fields(); ?>
                <?php self::render_thank_you_page_settings(); ?>
            </div>
        </div>
        <?php
    }

    private static function render_field_toggles() {
        $fields = array(
            'show_name' => 'إظهار حقل الاسم',
            'show_phone' => 'إظهار حقل الهاتف',
            'show_email' => 'إظهار حقل البريد الإلكتروني',
            'show_address' => 'إظهار حقل العنوان',
            'show_city' => 'إظهار حقل المدينة'
        );

        foreach ($fields as $field_id => $label) {
            $value = get_option($field_id, '1');
            ?>
            <div class="field-toggle flex items-center justify-between p-3 bg-gray-50 rounded">
                <label class="text-gray-700"><?php echo esc_html($label); ?></label>
                <label class="switch">
                    <input type="checkbox" 
                           name="<?php echo esc_attr($field_id); ?>" 
                           <?php checked($value, '1'); ?> 
                           class="field-toggle-input">
                    <span class="slider round"></span>
                </label>
            </div>
            <?php
        }
    }

    private static function render_custom_fields() {
        ?>
        <div class="custom-fields-section mt-6">
            <h4 class="text-lg font-medium mb-3">الحقول المخصصة</h4>
            <div id="custom-fields-container"></div>
            <button type="button" id="add-custom-field" 
                    class="mt-3 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition">
                إضافة حقل جديد
            </button>
        </div>
        <?php
    }

    private static function render_thank_you_page_settings() {
        $thank_you_page = get_option('custom_order_form_thank_you_page', '');
        ?>
        <div class="thank-you-page-settings mt-6">
            <h4 class="text-lg font-medium mb-3">إعدادات صفحة الشكر</h4>
            <div class="field-group bg-gray-50 p-4 rounded">
                <label class="block text-gray-700 mb-2">رابط صفحة الشكر</label>
                <input type="url"
                       name="thank_you_page"
                       value="<?php echo esc_url($thank_you_page); ?>"
                       placeholder="مثال: https://yoursite.com/thank-you"
                       class="w-full px-3 py-2 border rounded focus:border-primary focus:ring-1 focus:ring-primary">
                <p class="text-gray-500 text-sm mt-2">أدخل الرابط الكامل لصفحة الشكر المخصصة. اتركه فارغاً لاستخدام الصفحة الافتراضية.</p>
            </div>
        </div>
        <?php
    }
}