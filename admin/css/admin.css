.custom-order-form-settings {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.settings-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.settings-tab {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    color: #4a5568;
    font-weight: 500;
}

.settings-tab:hover {
    background: #f7fafc;
    color: #2d3748;
}

.settings-tab.active {
    background: #ebf4ff;
    color: #2563eb;
}

.settings-panel {
    display: none;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.settings-panel.active {
    display: block;
}

.settings-panel h2 {
    color: #1a202c;
    font-size: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.form-group {
    margin-bottom: 1.5rem;
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    transition: all 0.2s;
}

.form-group:hover {
    background: #f1f5f9;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: #2d3748;
}

.form-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: normal;
    cursor: pointer;
}

.form-group input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    margin: 0;
    border-radius: 4px;
    border-color: #cbd5e0;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s;
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group select:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-group .description {
    margin-top: 0.75rem;
    color: #718096;
    font-size: 0.875rem;
}

.button.button-primary {
    background: #2563eb;
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.button.button-primary:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

.button.button-primary:active {
    transform: translateY(0);
}

.sticky-save {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .sticky-save {
        left: 1rem;
        right: 1rem;
        width: calc(100% - 2rem);
        text-align: center;
        bottom: 1rem;
    }
}

.notice {
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 8px;
    border: none;
}

.notice.updated {
    background: #f0fff4;
    color: #2f855a;
    border-left: 4px solid #68d391;
}

.notice.error {
    background: #fff5f5;
    color: #c53030;
    border-left: 4px solid #fc8181;
}

/* تنسيق تبويب الطلبات */
.abandoned-orders,
.customers-list {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.abandoned-orders table,
.customers-list table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.abandoned-orders th,
.customers-list th {
    text-align: right;
    padding: 1rem;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #1a202c;
}

.abandoned-orders td,
.customers-list td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    color: #4a5568;
}

.abandoned-orders tr:hover td,
.customers-list tr:hover td {
    background: #f8fafc;
}

.abandoned-orders .button.delete-abandoned-order {
    color: #dc2626;
    border-color: #dc2626;
    background: transparent;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    transition: all 0.2s;
}

.abandoned-orders .button.delete-abandoned-order:hover {
    background: #dc2626;
    color: white;
}

.orders-message {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 8px;
    font-weight: 500;
}

.orders-message.success {
    background: #f0fff4;
    border: 1px solid #10b981;
    color: #047857;
}

.orders-message.error {
    background: #fff5f5;
    border: 1px solid #ef4444;
    color: #dc2626;
}

/* تنسيق قسم إدارة الحظر */
.block-management {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.add-block-form {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.add-block-form h3,
.blocked-list h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
    color: #1a202c;
    font-size: 1.1rem;
}

.add-block-form .form-group {
    margin-bottom: 1rem;
}

.add-block-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
}

.add-block-form .form-select,
.add-block-form .form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.95rem;
}

.add-block-form .form-select:focus,
.add-block-form .form-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.add-block-button {
    background: var(--primary-color) !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border: none !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    margin-top: 1rem !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
}

.add-block-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

.blocked-list {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.blocked-list table {
    width: 100%;
    border-collapse: collapse;
}

.blocked-list th {
    text-align: right;
    padding: 1rem;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #1a202c;
}

.blocked-list td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    color: #4a5568;
}

.remove-block-button {
    color: #dc2626 !important;
    border-color: #dc2626 !important;
    background: transparent !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 4px !important;
    transition: all 0.2s !important;
}

.remove-block-button:hover {
    background: #dc2626 !important;
    color: white !important;
}

.wp-picker-container {
    display: block;
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .block-management {
        grid-template-columns: 1fr;
    }
}

.wp-picker-container .wp-color-result {
    margin: 0;
    border-radius: 6px;
}

@media (max-width: 768px) {
    .custom-order-form-settings {
        margin: 1rem;
    }

    .settings-tabs {
        flex-wrap: wrap;
    }

    .settings-tab {
        flex: 1;
        text-align: center;
        padding: 0.5rem;
    }

    .settings-panel {
        padding: 1.5rem;
    }

    .form-group {
        padding: 1rem;
    }
}

/* تنسيق جدول الألوان */
.color-settings-table {
    margin-bottom: 1rem;
    border-collapse: collapse;
}

.color-settings-table input[type="text"] {
    width: 100%;
}

.color-settings-table input[type="color"] {
    width: 100%;
    height: 35px;
    padding: 0;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
}

.color-preview {
    width: 100px;
    height: 35px;
    border-radius: 4px;
    margin-right: 1rem;
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #e2e8f0;
}

.delete-color {
    color: #dc2626 !important;
    border-color: #dc2626 !important;
    background: transparent !important;
    margin-right: 1rem;
}

.delete-color:hover {
    background: #dc2626 !important;
    color: white !important;
}

.add-new-color {
    margin-top: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
}

.add-new-color:hover {
    background: var(--button-color);
}

/* تنسيق جدول أسعار التوصيل */
.shipping-prices-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
}

.shipping-prices-table th {
    text-align: right;
    padding: 0.75rem;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #1a202c;
}

.shipping-prices-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.shipping-prices-table input[type="number"] {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    margin-left: 0.5rem;
}

.disable-shipping {
    display: inline-flex;
    align-items: center;
    margin-right: 0.75rem;
    font-size: 0.85rem;
    color: #718096;
    cursor: pointer;
}

.disable-shipping input[type="checkbox"] {
    margin-left: 0.25rem;
}

.reset-shipping-prices {
    background-color: #f3f4f6;
    color: #4b5563;
    border: 1px solid #d1d5db;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.reset-shipping-prices:hover {
    background-color: #e5e7eb;
    color: #1f2937;
}

/* تنسيق قسم استيراد/تصدير CSV */
.csv-import-export {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 1.5rem;
}

.csv-import-export h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #1f2937;
}

.csv-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.csv-upload-wrapper {
    position: relative;
}

.upload-csv-button {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.upload-csv-button:hover {
    background-color: #2563eb !important;
}

.download-csv-template {
    background-color: #10b981 !important;
    color: white !important;
    border-color: #10b981 !important;
}

.download-csv-template:hover {
    background-color: #059669 !important;
}

.csv-instructions {
    background-color: #f3f4f6;
    border-left: 4px solid #9ca3af;
    padding: 1rem;
    margin-top: 1rem;
    border-radius: 4px;
}

.csv-instructions p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #4b5563;
}

.csv-instructions code {
    background-color: #e5e7eb;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: monospace;
}

#csv-upload-result {
    padding: 0.75rem;
    border-radius: 4px;
    font-weight: 500;
}

#csv-upload-result.success {
    background-color: #d1fae5;
    border: 1px solid #10b981;
    color: #047857;
}

#csv-upload-result.error {
    background-color: #fee2e2;
    border: 1px solid #ef4444;
    color: #b91c1c;
}
