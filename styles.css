:root {
    --primary-color: #8A2BE2;
    --button-color: #3b82f6;
    --text-color: #1f2937;
    --background-color: #ffffff;
    --border-color: #e5e7eb;
    --border-radius: 8px;
    --font-family: 'IBM Plex Sans Arabic', sans-serif;
}

body {
    font-family: var(--font-family);
    color: var(--text-color);
    background-color: #f3f4f6;
}

.custom-order-form-container {
    max-width: 600px;
    margin: 0.5rem auto;
    padding: 0 0.25rem;
}

@media (max-width: 768px) {
    .custom-order-form-container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }
}

.custom-order-form {
    background: var(--background-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
}

.custom-order-form h2 {
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: right;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.custom-order-form h2 i {
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-control, .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    width: 100%;
    font-size: 0.95rem;
    background-color: #fff;
    height: 48px; /* زيادة الارتفاع */
    line-height: 1.2; /* تحسين ارتفاع النص */
}

/* تحسين مظهر القوائم المنسدلة */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%236b7280' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 12px;
    padding-left: 2rem; /* مساحة للسهم */
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input-group {
    display: flex;
    align-items: center;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.input-group .form-control,
.input-group .form-select {
    border: none;
    border-radius: 0;
    flex: 1;
    height: 48px; /* زيادة الارتفاع */
    padding-top: 0;
    padding-bottom: 0;
}

.input-group-text {
    padding: 0.75rem;
    background-color: #f9fafb;
    border-left: 1px solid var(--border-color);
    color: var(--primary-color);
    width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
}

/* توجيه النص في حقل الهاتف */
#phone::placeholder {
    direction: rtl;
}

.variations-group {
    margin-bottom: 1.5rem;
}

.variations-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    font-size: 0.95rem;
    color: var(--text-color);
}

.variation-group {
    margin-bottom: 1rem;
}

.variation-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.swatch-option {
    cursor: pointer;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    position: relative;
}

.swatch-option.selected {
    border-color: var(--primary-color);
}

.color-swatch {
    width: 40px;
    height: 40px;
    position: relative;
    overflow: hidden;
}

.color-swatch .swatch-label {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    opacity: 0;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.color-swatch:hover .swatch-label {
    bottom: 2px;
    opacity: 1;
}

.text-swatch {
    padding: 0.5rem 1rem;
    background: #fff;
    font-size: 0.9rem;
    color: var(--text-color);
}

.text-swatch:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.text-swatch.selected {
    background: var(--primary-color);
    color: white;
}

/* تنسيق متغيرات الصور */
.image-swatch {
    width: 80px;
    height: 100px;
    padding: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    background: #fff;
    position: relative;
}

.image-swatch img {
    width: 100%;
    height: 70px;
    object-fit: contain;
    border-radius: 4px;
}

.image-swatch .swatch-label {
    font-size: 0.75rem;
    text-align: center;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.image-swatch:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-swatch.selected {
    border-color: var(--primary-color);
    background-color: #f0f9ff;
}

.image-swatch.selected::after {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 4px;
    background: var(--primary-color);
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

/* تنسيق متغيرات الحجم */
.size-swatch {
    padding: 8px 16px;
    min-width: 60px;
    text-align: center;
    background: #fff;
    font-size: 0.9rem;
    font-weight: 500;
}

.size-swatch:hover {
    background: #f0f9ff;
    border-color: var(--primary-color);
}

.size-swatch.selected {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.color-swatch.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
}

/* زر الشراء المثبت */

.sticky-buy-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 1rem;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    text-align: center;
}

.sticky-buy-button.visible {
    transform: translateY(0);
}

.sticky-buy-button button {
    background: linear-gradient(135deg, var(--primary-color), var(--button-color));
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-family);
    min-width: 200px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sticky-buy-button button:hover {
    background: linear-gradient(135deg, var(--button-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

@media (max-width: 768px) {
    .sticky-buy-button {
        padding: 0.75rem;
    }

    .sticky-buy-button button {
        width: 100%;
        padding: 0.875rem;
    }
}

.delivery-type-group {
    margin: 1.5rem 0;
}

.delivery-type-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    font-size: 0.95rem;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
    width: 100%;
}

.btn-check {
    display: none;
}

.btn-check + label {
    flex: 1;
    text-align: center;
    padding: 0.5rem;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.95rem;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-check:checked + label {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.d-flex {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: nowrap;
}

.flex-grow-1 {
    flex-grow: 1;
}

.custom-quantity-control {
    display: flex;
    align-items: stretch;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    width: 120px;
    min-width: 120px;
    overflow: hidden;
    height: 42px;
}

.custom-quantity-control button {
    width: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: #f9fafb;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.875rem;
}

.custom-quantity-control input {
    width: 48px;
    text-align: center;
    border: none;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    font-size: 0.95rem;
    padding: 0;
    background: #fff;
}

.btn {
    padding: 0 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    height: 42px;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--button-color));
    border: none;
    color: white;
}

#confirmOrderLoading {
    display: none !important;
}

#confirmOrderLoading.show {
    display: flex !important;
    align-items: center;
    gap: 0.5rem;
}

.btn-success {
    background: linear-gradient(45deg, #10b981, #059669);
    border: none;
    color: white;
    margin: 0.75rem auto;
    max-width: 300px;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
    background: linear-gradient(45deg, #059669, #047857);
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
}

.add-to-cart-btn {
    margin: 0.75rem 0;
    background: linear-gradient(135deg, var(--primary-color), var(--button-color));
    transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
    background: linear-gradient(135deg, var(--button-color), var(--primary-color));
    transform: translateY(-2px);
}

.add-to-cart-btn i {
    margin-left: 0.5rem;
}

/* تنسيق التوصيل المجاني */
.free-shipping {
    display: inline-block;
    background-color: #10b981;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    animation: pulse 1.5s infinite;
}

/* تنسيق رسالة التوصيل غير متوفر */
.shipping-unavailable {
    display: inline-block;
    background-color: #ef4444;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

.form-group {
    position: relative;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
    text-align: right;
}

.input-group.error {
    border-color: #ef4444;
}

.input-group.error .input-group-text {
    border-color: #ef4444;
    color: #ef4444;
}

.input-group.error .form-control,
.input-group.error .form-select {
    border-color: #ef4444;
}

.input-group.error .form-control:focus,
.input-group.error .form-select:focus {
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.order-summary {
    margin-top: 1.5rem;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.order-summary h3 {
    padding: 1rem 1.5rem;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    background: linear-gradient(to left, #f8f9fa, #ffffff);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
}

.order-summary h3 i {
    font-size: 0.875rem;
    transition: transform 0.2s;
}

.order-summary h3 i.rotated {
    transform: rotate(180deg);
}

#summaryContent {
    padding: 1.5rem;
}

#summaryContent p {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
    font-size: 1rem;
    color: #4b5563;
    padding-bottom: 0.5rem;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

#summaryContent p:last-child {
    border-bottom: none;
    padding-top: 0.75rem;
    margin-top: 0.5rem;
    border-top: 2px solid var(--border-color);
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

#summaryContent p span.waiting-state {
    color: #9ca3af;
    font-style: italic;
}

#summaryContent p:last-child {
    margin-bottom: 0;
    color: var(--text-color);
    font-weight: 500;
}

/* تنسيق السعر المشطوب */
.old-price {
    text-decoration: line-through;
    color: #9ca3af;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.price-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price-inline-wrapper {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

#summaryContent p i {
    color: #6b7280;
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
}

@media (max-width: 768px) {
    .custom-order-form {
        padding: 1rem;
        border-radius: 0;
        margin: 0;
    }

    .btn-group {
        flex-direction: row;
    }

    .d-flex {
        flex-direction: row;
        width: 100%;
        gap: 0.5rem;
        justify-content: space-between;
    }

    .custom-quantity-control {
        width: 90px;
        min-width: 90px;
        margin-bottom: 0;
    }

    .btn-primary {
        flex: 1;
        padding: 0 1rem;
    }

    /* تحسين ملخص الطلب على الهاتف */
    .order-summary {
        margin: 1rem 0;
        border-radius: var(--border-radius);
    }

    #summaryContent {
        padding: 1rem;
    }

    .form-group {
        margin-bottom: 0.75rem;
    }

    /* تحسين المسافات بين الحقول */
    .input-group + .input-group {
        margin-top: 0.5rem;
    }

    /* تعديل حجم أزرار الكمية */
    .custom-quantity-control button {
        width: 28px;
    }

    .custom-quantity-control input {
        width: 34px;
    }
}
