/**
 * Premium scripts for Custom Order Form
 * This file will only be included in the premium version of the plugin
 */

(function($) {
    'use strict';

    // تهيئة الرسوم البيانية للتحليلات
    window.initCharts = function() {
        // بيانات المبيعات
        const salesCtx = document.getElementById('sales-chart');
        if (salesCtx) {
            const salesChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [12, 19, 3, 5, 2, 3, 15],
                        backgroundColor: 'rgba(37, 99, 235, 0.2)',
                        borderColor: 'rgba(37, 99, 235, 1)',
                        borderWidth: 2,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // بيانات معدل التحويل
        const conversionCtx = document.getElementById('conversion-chart');
        if (conversionCtx) {
            const conversionChart = new Chart(conversionCtx, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
                    datasets: [{
                        label: 'معدل التحويل %',
                        data: [65, 59, 80, 81, 56, 55, 70],
                        backgroundColor: 'rgba(37, 99, 235, 0.6)',
                        borderColor: 'rgba(37, 99, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
    };

    // تحديث بيانات التحليلات
    window.updateAnalyticsData = function(days) {
        // إرسال طلب AJAX لجلب البيانات الجديدة
        $.ajax({
            url: woocommerce_params.ajax_url,
            type: 'POST',
            data: {
                action: 'get_analytics_data',
                days: days,
                nonce: woocommerce_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    // تحديث قيم التحليلات
                    $('.analytics-value').first().text(response.data.total_sales);
                    $('.analytics-value').eq(1).text(response.data.conversion_rate + '%');
                    
                    // تحديث قائمة المنتجات الأكثر مبيعاً
                    let topProductsHtml = '';
                    response.data.top_products.forEach(function(product) {
                        topProductsHtml += `<li>
                            <span class="product-name">${product.name}</span>
                            <span class="product-sales">${product.sales}</span>
                        </li>`;
                    });
                    $('.top-products').html(topProductsHtml);
                    
                    // تحديث الرسوم البيانية
                    updateCharts(response.data);
                }
            }
        });
    };

    // تحديث الرسوم البيانية
    function updateCharts(data) {
        // تحديث رسم بياني المبيعات
        const salesChart = Chart.getChart('sales-chart');
        if (salesChart) {
            salesChart.data.labels = data.sales_chart.labels;
            salesChart.data.datasets[0].data = data.sales_chart.data;
            salesChart.update();
        }
        
        // تحديث رسم بياني معدل التحويل
        const conversionChart = Chart.getChart('conversion-chart');
        if (conversionChart) {
            conversionChart.data.labels = data.conversion_chart.labels;
            conversionChart.data.datasets[0].data = data.conversion_chart.data;
            conversionChart.update();
        }
    }

    // إضافة وظائف للحقول المتميزة في النموذج
    $(document).ready(function() {
        // تفعيل حقل التاريخ
        const scheduledDelivery = $('#scheduled_delivery');
        if (scheduledDelivery.length) {
            // تعيين الحد الأدنى للتاريخ ليكون غداً
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            scheduledDelivery.attr('min', tomorrow.toISOString().split('T')[0]);
            
            // إضافة مستمع لتغيير التاريخ
            scheduledDelivery.on('change', function() {
                const selectedDate = new Date($(this).val());
                const dayOfWeek = selectedDate.getDay();
                
                // التحقق من أن التاريخ ليس يوم الجمعة (5) أو السبت (6)
                if (dayOfWeek === 5 || dayOfWeek === 6) {
                    showNotification('تنبيه', 'لا يتوفر التوصيل في أيام الجمعة والسبت. يرجى اختيار تاريخ آخر.', 'warning');
                    $(this).val(''); // إعادة تعيين القيمة
                }
            });
        }
        
        // تفعيل خيارات الدفع
        $('input[name="payment_option"]').on('change', function() {
            const paymentOption = $(this).val();
            
            if (paymentOption === 'prepaid') {
                // إظهار خيارات الدفع المسبق
                if ($('.payment-methods').length === 0) {
                    const paymentMethodsHtml = `
                        <div class="form-group payment-methods">
                            <label>اختر طريقة الدفع</label>
                            <div class="payment-options">
                                <label>
                                    <input type="radio" name="payment_method" value="bank_transfer" checked>
                                    تحويل بنكي
                                </label>
                                <label>
                                    <input type="radio" name="payment_method" value="credit_card">
                                    بطاقة ائتمان
                                </label>
                                <label>
                                    <input type="radio" name="payment_method" value="paypal">
                                    PayPal
                                </label>
                            </div>
                        </div>
                    `;
                    $(this).closest('.form-group').after(paymentMethodsHtml);
                }
            } else {
                // إخفاء خيارات الدفع المسبق
                $('.payment-methods').remove();
            }
        });
        
        // إضافة التحقق من صحة الحقول المتميزة
        $('#custom-order-form').on('submit', function(e) {
            const paymentOption = $('input[name="payment_option"]:checked').val();
            
            if (paymentOption === 'prepaid') {
                const paymentMethod = $('input[name="payment_method"]:checked').val();
                
                if (!paymentMethod) {
                    e.preventDefault();
                    showNotification('خطأ', 'يرجى اختيار طريقة الدفع', 'error');
                    return false;
                }
            }
            
            // إضافة البيانات المتميزة إلى النموذج
            const formData = $(this).serializeArray();
            const premiumData = {
                notes: $('#notes').val(),
                scheduled_delivery: $('#scheduled_delivery').val(),
                payment_option: paymentOption,
                payment_method: $('input[name="payment_method"]:checked').val()
            };
            
            // إضافة البيانات المتميزة إلى بيانات الطلب
            $('#order_data').val(JSON.stringify({
                ...JSON.parse($('#order_data').val() || '{}'),
                premium_data: premiumData
            }));
        });
    });
    
    // إظهار الإشعارات المتميزة
    function showNotification(title, message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = $(`
            <div class="premium-notification">
                <div class="premium-notification-icon">
                    <i class="fas fa-${type === 'error' ? 'exclamation-circle' : (type === 'warning' ? 'exclamation-triangle' : 'info-circle')}"></i>
                </div>
                <div class="premium-notification-content">
                    <h4>${title}</h4>
                    <p>${message}</p>
                </div>
                <div class="premium-notification-close">
                    <i class="fas fa-times"></i>
                </div>
            </div>
        `);
        
        // إضافة الإشعار إلى الصفحة
        $('body').append(notification);
        
        // إظهار الإشعار
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        // إخفاء الإشعار بعد 5 ثوانٍ
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 5000);
        
        // إغلاق الإشعار عند النقر على زر الإغلاق
        notification.find('.premium-notification-close').on('click', function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        });
    }
    
    // إضافة معالج AJAX لجلب بيانات التحليلات
    $(document).on('click', '.settings-tab[data-tab="analytics"]', function() {
        // تهيئة الرسوم البيانية عند النقر على علامة التبويب
        setTimeout(function() {
            initCharts();
        }, 100);
    });
    
})(jQuery);
