<?php
/**
 * Premium features for Custom Order Form
 * This file will only be included in the premium version of the plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add premium features to the form
 */
function add_premium_features_to_form() {
    // تحقق من أن المستخدم لديه ترخيص صالح
    if (!sf_fs()->is_paying()) {
        return;
    }

    // إضافة ميزات متقدمة للنموذج
    add_action('custom_order_form_after_fields', 'add_premium_form_fields');
    
    // إضافة خيارات متقدمة للإدارة
    add_action('custom_order_form_admin_tabs', 'add_premium_admin_tabs');
    
    // إضافة تحليلات متقدمة
    add_action('custom_order_form_admin_dashboard', 'add_premium_analytics');
}
add_action('init', 'add_premium_features_to_form');

/**
 * Add premium fields to the form
 */
function add_premium_form_fields() {
    ?>
    <div class="premium-fields">
        <h3>خيارات متقدمة</h3>
        
        <!-- حقل ملاحظات متقدم -->
        <div class="form-group">
            <label for="notes">ملاحظات إضافية</label>
            <textarea id="notes" name="notes" rows="4" placeholder="أضف أي ملاحظات إضافية هنا..."></textarea>
        </div>
        
        <!-- خيار التوصيل المجدول -->
        <div class="form-group">
            <label for="scheduled_delivery">موعد التوصيل المفضل</label>
            <input type="date" id="scheduled_delivery" name="scheduled_delivery" min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
        </div>
        
        <!-- خيار الدفع المسبق -->
        <div class="form-group">
            <label>خيارات الدفع</label>
            <div class="radio-group">
                <label>
                    <input type="radio" name="payment_option" value="cod" checked>
                    الدفع عند الاستلام
                </label>
                <label>
                    <input type="radio" name="payment_option" value="prepaid">
                    الدفع المسبق
                </label>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Add premium tabs to admin page
 */
function add_premium_admin_tabs() {
    ?>
    <button type="button" class="settings-tab" data-tab="analytics">التحليلات</button>
    <button type="button" class="settings-tab" data-tab="advanced">إعدادات متقدمة</button>
    <?php
}

/**
 * Add premium analytics to admin dashboard
 */
function add_premium_analytics() {
    ?>
    <div class="settings-panel" id="analytics-panel">
        <h2>تحليلات الطلبات</h2>
        
        <div class="analytics-container">
            <div class="analytics-card">
                <h3>إجمالي المبيعات</h3>
                <div class="analytics-value"><?php echo get_total_sales(); ?></div>
                <div class="analytics-chart">
                    <!-- رسم بياني للمبيعات -->
                    <canvas id="sales-chart"></canvas>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3>معدل التحويل</h3>
                <div class="analytics-value"><?php echo get_conversion_rate(); ?>%</div>
                <div class="analytics-chart">
                    <!-- رسم بياني لمعدل التحويل -->
                    <canvas id="conversion-chart"></canvas>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3>المنتجات الأكثر مبيعاً</h3>
                <ul class="top-products">
                    <?php foreach (get_top_products() as $product) : ?>
                    <li>
                        <span class="product-name"><?php echo $product['name']; ?></span>
                        <span class="product-sales"><?php echo $product['sales']; ?></span>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        
        <div class="date-filter">
            <label>تصفية حسب التاريخ:</label>
            <select id="date-range">
                <option value="7">آخر 7 أيام</option>
                <option value="30" selected>آخر 30 يوم</option>
                <option value="90">آخر 3 أشهر</option>
                <option value="365">آخر سنة</option>
            </select>
        </div>
        
        <script>
            // كود JavaScript لإنشاء الرسوم البيانية
            jQuery(document).ready(function($) {
                // تهيئة الرسوم البيانية
                initCharts();
                
                // تحديث البيانات عند تغيير نطاق التاريخ
                $('#date-range').on('change', function() {
                    updateAnalyticsData($(this).val());
                });
            });
        </script>
    </div>
    
    <div class="settings-panel" id="advanced-panel">
        <h2>إعدادات متقدمة</h2>
        
        <div class="form-group">
            <label>تخصيص رسائل البريد الإلكتروني</label>
            <div class="toggle-switch">
                <input type="checkbox" id="custom_emails" name="advanced[custom_emails]" value="1" <?php checked(get_option('custom_order_form_advanced_custom_emails', false)); ?>>
                <label for="custom_emails"></label>
            </div>
            <p class="description">تمكين تخصيص رسائل البريد الإلكتروني للطلبات</p>
        </div>
        
        <div class="form-group">
            <label>تكامل SMS</label>
            <div class="toggle-switch">
                <input type="checkbox" id="sms_integration" name="advanced[sms_integration]" value="1" <?php checked(get_option('custom_order_form_advanced_sms', false)); ?>>
                <label for="sms_integration"></label>
            </div>
            <p class="description">إرسال إشعارات SMS للعملاء عند تغيير حالة الطلب</p>
        </div>
        
        <div class="form-group">
            <label>رقم هاتف SMS</label>
            <input type="text" name="advanced[sms_phone]" value="<?php echo esc_attr(get_option('custom_order_form_advanced_sms_phone', '')); ?>" placeholder="أدخل رقم هاتف لإرسال رسائل SMS">
        </div>
        
        <div class="form-group">
            <label>تصدير التقارير تلقائياً</label>
            <div class="toggle-switch">
                <input type="checkbox" id="auto_export" name="advanced[auto_export]" value="1" <?php checked(get_option('custom_order_form_advanced_auto_export', false)); ?>>
                <label for="auto_export"></label>
            </div>
            <p class="description">تصدير تقارير الطلبات تلقائياً كل أسبوع</p>
        </div>
        
        <div class="form-group">
            <label>البريد الإلكتروني لاستلام التقارير</label>
            <input type="email" name="advanced[export_email]" value="<?php echo esc_attr(get_option('custom_order_form_advanced_export_email', '')); ?>" placeholder="أدخل البريد الإلكتروني لاستلام التقارير">
        </div>
    </div>
    <?php
}

/**
 * Get total sales for analytics
 */
function get_total_sales() {
    global $wpdb;
    
    $total = $wpdb->get_var("
        SELECT SUM(meta_value) 
        FROM {$wpdb->postmeta} 
        WHERE meta_key = '_order_total' 
        AND post_id IN (
            SELECT ID FROM {$wpdb->posts} 
            WHERE post_type = 'shop_order' 
            AND post_status IN ('wc-completed', 'wc-processing')
        )
    ");
    
    return wc_price($total ?: 0);
}

/**
 * Get conversion rate for analytics
 */
function get_conversion_rate() {
    // عدد الطلبات المكتملة
    $completed_orders = wc_orders_count('completed') + wc_orders_count('processing');
    
    // عدد الطلبات المتروكة
    $abandoned_orders = count(get_option('custom_order_form_abandoned_orders', array()));
    
    // حساب معدل التحويل
    $total_attempts = $completed_orders + $abandoned_orders;
    
    if ($total_attempts == 0) {
        return 0;
    }
    
    return round(($completed_orders / $total_attempts) * 100, 2);
}

/**
 * Get top selling products for analytics
 */
function get_top_products() {
    global $wpdb;
    
    $results = $wpdb->get_results("
        SELECT p.post_title as name, SUM(oim.meta_value) as quantity
        FROM {$wpdb->prefix}woocommerce_order_items oi
        JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
        JOIN {$wpdb->posts} o ON o.ID = oi.order_id
        JOIN {$wpdb->posts} p ON p.ID = oim.meta_value
        WHERE oi.order_item_type = 'line_item'
        AND oim.meta_key = '_product_id'
        AND o.post_type = 'shop_order'
        AND o.post_status IN ('wc-completed', 'wc-processing')
        GROUP BY p.ID
        ORDER BY quantity DESC
        LIMIT 5
    ");
    
    $products = array();
    
    if ($results) {
        foreach ($results as $result) {
            $products[] = array(
                'name' => $result->name,
                'sales' => $result->quantity
            );
        }
    }
    
    return $products;
}

/**
 * Save advanced settings
 */
function save_advanced_settings($settings) {
    if (isset($settings['advanced'])) {
        $advanced = $settings['advanced'];
        
        update_option('custom_order_form_advanced_custom_emails', isset($advanced['custom_emails']));
        update_option('custom_order_form_advanced_sms', isset($advanced['sms_integration']));
        update_option('custom_order_form_advanced_sms_phone', sanitize_text_field($advanced['sms_phone'] ?? ''));
        update_option('custom_order_form_advanced_auto_export', isset($advanced['auto_export']));
        update_option('custom_order_form_advanced_export_email', sanitize_email($advanced['export_email'] ?? ''));
    }
}
add_action('custom_order_form_save_settings', 'save_advanced_settings');

/**
 * Enqueue premium scripts and styles
 */
function enqueue_premium_assets() {
    wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.7.0', true);
    wp_enqueue_script('premium-scripts', plugin_dir_url(__FILE__) . 'js/premium-scripts.js', array('jquery', 'chart-js'), '1.0', true);
    wp_enqueue_style('premium-styles', plugin_dir_url(__FILE__) . 'css/premium-styles.css', array(), '1.0');
}
add_action('admin_enqueue_scripts', 'enqueue_premium_assets');
