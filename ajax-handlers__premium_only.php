<?php
/**
 * Premium AJAX handlers for Custom Order Form
 * This file will only be included in the premium version of the plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get analytics data for the admin dashboard
 */
function get_premium_analytics_data() {
    // التحقق من الأمان
    if (!check_ajax_referer('custom_order_form_admin_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid security token');
        return;
    }

    // التحقق من الصلاحيات
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // الحصول على عدد الأيام من الطلب
    $days = isset($_POST['days']) ? intval($_POST['days']) : 30;

    // الحصول على بيانات المبيعات
    $sales_data = get_sales_data_for_period($days);
    
    // الحصول على بيانات معدل التحويل
    $conversion_data = get_conversion_data_for_period($days);
    
    // الحصول على المنتجات الأكثر مبيعاً
    $top_products = get_top_products_for_period($days);
    
    // إرجاع البيانات
    wp_send_json_success(array(
        'total_sales' => wc_price($sales_data['total']),
        'conversion_rate' => $conversion_data['rate'],
        'top_products' => $top_products,
        'sales_chart' => array(
            'labels' => $sales_data['labels'],
            'data' => $sales_data['data']
        ),
        'conversion_chart' => array(
            'labels' => $conversion_data['labels'],
            'data' => $conversion_data['data']
        )
    ));
}
add_action('wp_ajax_get_analytics_data', 'get_premium_analytics_data');

/**
 * Get sales data for a specific period
 */
function get_sales_data_for_period($days) {
    global $wpdb;
    
    // تحديد تاريخ البداية
    $start_date = date('Y-m-d', strtotime("-$days days"));
    
    // الحصول على إجمالي المبيعات
    $total_sales = $wpdb->get_var($wpdb->prepare("
        SELECT SUM(meta_value) 
        FROM {$wpdb->postmeta} 
        WHERE meta_key = '_order_total' 
        AND post_id IN (
            SELECT ID FROM {$wpdb->posts} 
            WHERE post_type = 'shop_order' 
            AND post_status IN ('wc-completed', 'wc-processing')
            AND post_date >= %s
        )
    ", $start_date));
    
    // الحصول على بيانات المبيعات اليومية
    $daily_sales = $wpdb->get_results($wpdb->prepare("
        SELECT 
            DATE(post_date) as order_date,
            SUM(meta_value) as daily_total
        FROM {$wpdb->posts} p
        JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE 
            p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing')
            AND pm.meta_key = '_order_total'
            AND p.post_date >= %s
        GROUP BY DATE(post_date)
        ORDER BY order_date ASC
    ", $start_date));
    
    // تجهيز البيانات للرسم البياني
    $labels = array();
    $data = array();
    
    // إنشاء مصفوفة بجميع التواريخ في النطاق
    $current_date = new DateTime($start_date);
    $end_date = new DateTime();
    $interval = new DateInterval('P1D');
    $date_range = new DatePeriod($current_date, $interval, $end_date);
    
    // تحويل نتائج الاستعلام إلى مصفوفة مفهرسة بالتاريخ
    $sales_by_date = array();
    foreach ($daily_sales as $sale) {
        $sales_by_date[$sale->order_date] = $sale->daily_total;
    }
    
    // ملء المصفوفات بالبيانات
    foreach ($date_range as $date) {
        $date_str = $date->format('Y-m-d');
        $labels[] = $date->format('d/m');
        $data[] = isset($sales_by_date[$date_str]) ? floatval($sales_by_date[$date_str]) : 0;
    }
    
    return array(
        'total' => floatval($total_sales),
        'labels' => $labels,
        'data' => $data
    );
}

/**
 * Get conversion rate data for a specific period
 */
function get_conversion_data_for_period($days) {
    global $wpdb;
    
    // تحديد تاريخ البداية
    $start_date = date('Y-m-d', strtotime("-$days days"));
    
    // الحصول على عدد الطلبات المكتملة
    $completed_orders = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(ID)
        FROM {$wpdb->posts}
        WHERE post_type = 'shop_order'
        AND post_status IN ('wc-completed', 'wc-processing')
        AND post_date >= %s
    ", $start_date));
    
    // الحصول على عدد الطلبات المتروكة
    $abandoned_orders = count(array_filter(get_option('custom_order_form_abandoned_orders', array()), function($order) use ($start_date) {
        return isset($order['date']) && strtotime($order['date']) >= strtotime($start_date);
    }));
    
    // حساب معدل التحويل
    $total_attempts = $completed_orders + $abandoned_orders;
    $conversion_rate = ($total_attempts > 0) ? round(($completed_orders / $total_attempts) * 100, 2) : 0;
    
    // الحصول على بيانات معدل التحويل اليومية
    $daily_completed = $wpdb->get_results($wpdb->prepare("
        SELECT 
            DATE(post_date) as order_date,
            COUNT(ID) as completed_count
        FROM {$wpdb->posts}
        WHERE 
            post_type = 'shop_order'
            AND post_status IN ('wc-completed', 'wc-processing')
            AND post_date >= %s
        GROUP BY DATE(post_date)
        ORDER BY order_date ASC
    ", $start_date));
    
    // تجميع الطلبات المتروكة حسب التاريخ
    $abandoned_by_date = array();
    $abandoned_orders_data = get_option('custom_order_form_abandoned_orders', array());
    
    foreach ($abandoned_orders_data as $order) {
        if (isset($order['date']) && strtotime($order['date']) >= strtotime($start_date)) {
            $date = date('Y-m-d', strtotime($order['date']));
            if (!isset($abandoned_by_date[$date])) {
                $abandoned_by_date[$date] = 0;
            }
            $abandoned_by_date[$date]++;
        }
    }
    
    // تحويل نتائج الاستعلام إلى مصفوفة مفهرسة بالتاريخ
    $completed_by_date = array();
    foreach ($daily_completed as $completed) {
        $completed_by_date[$completed->order_date] = $completed->completed_count;
    }
    
    // تجهيز البيانات للرسم البياني
    $labels = array();
    $data = array();
    
    // إنشاء مصفوفة بجميع التواريخ في النطاق
    $current_date = new DateTime($start_date);
    $end_date = new DateTime();
    $interval = new DateInterval('P1D');
    $date_range = new DatePeriod($current_date, $interval, $end_date);
    
    // ملء المصفوفات بالبيانات
    foreach ($date_range as $date) {
        $date_str = $date->format('Y-m-d');
        $labels[] = $date->format('d/m');
        
        $completed = isset($completed_by_date[$date_str]) ? $completed_by_date[$date_str] : 0;
        $abandoned = isset($abandoned_by_date[$date_str]) ? $abandoned_by_date[$date_str] : 0;
        $total = $completed + $abandoned;
        
        $data[] = ($total > 0) ? round(($completed / $total) * 100, 2) : 0;
    }
    
    return array(
        'rate' => $conversion_rate,
        'labels' => $labels,
        'data' => $data
    );
}

/**
 * Get top products for a specific period
 */
function get_top_products_for_period($days) {
    global $wpdb;
    
    // تحديد تاريخ البداية
    $start_date = date('Y-m-d', strtotime("-$days days"));
    
    // الحصول على المنتجات الأكثر مبيعاً
    $results = $wpdb->get_results($wpdb->prepare("
        SELECT 
            p.post_title as name,
            SUM(oim.meta_value) as quantity
        FROM {$wpdb->prefix}woocommerce_order_items oi
        JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
        JOIN {$wpdb->posts} o ON o.ID = oi.order_id
        JOIN {$wpdb->posts} p ON p.ID = oim.meta_value
        WHERE 
            oi.order_item_type = 'line_item'
            AND oim.meta_key = '_product_id'
            AND o.post_type = 'shop_order'
            AND o.post_status IN ('wc-completed', 'wc-processing')
            AND o.post_date >= %s
        GROUP BY p.ID
        ORDER BY quantity DESC
        LIMIT 5
    ", $start_date));
    
    $products = array();
    
    if ($results) {
        foreach ($results as $result) {
            $products[] = array(
                'name' => $result->name,
                'sales' => $result->quantity
            );
        }
    }
    
    return $products;
}

/**
 * Process premium payment methods
 */
function process_premium_payment() {
    // التحقق من الأمان
    if (!check_ajax_referer('custom_order_form_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid security token');
        return;
    }
    
    // الحصول على بيانات الطلب
    $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
    $payment_method = isset($_POST['payment_method']) ? sanitize_text_field($_POST['payment_method']) : '';
    
    if (!$order_id || !$payment_method) {
        wp_send_json_error('Missing required data');
        return;
    }
    
    $order = wc_get_order($order_id);
    
    if (!$order) {
        wp_send_json_error('Invalid order');
        return;
    }
    
    // معالجة طريقة الدفع
    switch ($payment_method) {
        case 'bank_transfer':
            // تحديث حالة الطلب وإضافة ملاحظة
            $order->update_status('on-hold', 'Awaiting bank transfer');
            $order->add_order_note('Customer selected bank transfer payment method');
            $order->update_meta_data('_payment_method', 'bank_transfer');
            $order->update_meta_data('_payment_method_title', 'Bank Transfer');
            break;
            
        case 'credit_card':
            // تحديث حالة الطلب وإضافة ملاحظة
            $order->update_status('on-hold', 'Awaiting credit card payment');
            $order->add_order_note('Customer selected credit card payment method');
            $order->update_meta_data('_payment_method', 'credit_card');
            $order->update_meta_data('_payment_method_title', 'Credit Card');
            break;
            
        case 'paypal':
            // تحديث حالة الطلب وإضافة ملاحظة
            $order->update_status('on-hold', 'Awaiting PayPal payment');
            $order->add_order_note('Customer selected PayPal payment method');
            $order->update_meta_data('_payment_method', 'paypal');
            $order->update_meta_data('_payment_method_title', 'PayPal');
            break;
            
        default:
            wp_send_json_error('Invalid payment method');
            return;
    }
    
    $order->save();
    
    // إرجاع رابط صفحة الدفع المناسبة
    $payment_url = '';
    
    switch ($payment_method) {
        case 'bank_transfer':
            $payment_url = add_query_arg(array(
                'order_id' => $order_id,
                'payment' => 'bank_transfer'
            ), get_option('custom_order_form_payment_page', home_url()));
            break;
            
        case 'credit_card':
            $payment_url = add_query_arg(array(
                'order_id' => $order_id,
                'payment' => 'credit_card'
            ), get_option('custom_order_form_payment_page', home_url()));
            break;
            
        case 'paypal':
            $payment_url = add_query_arg(array(
                'order_id' => $order_id,
                'payment' => 'paypal'
            ), get_option('custom_order_form_payment_page', home_url()));
            break;
    }
    
    wp_send_json_success(array(
        'redirect_url' => $payment_url
    ));
}
add_action('wp_ajax_process_premium_payment', 'process_premium_payment');
add_action('wp_ajax_nopriv_process_premium_payment', 'process_premium_payment');

/**
 * Export orders to CSV
 */
function export_orders_to_csv() {
    // التحقق من الأمان
    if (!check_ajax_referer('custom_order_form_admin_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid security token');
        return;
    }
    
    // التحقق من الصلاحيات
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }
    
    // الحصول على نطاق التاريخ
    $start_date = isset($_POST['start_date']) ? sanitize_text_field($_POST['start_date']) : date('Y-m-d', strtotime('-30 days'));
    $end_date = isset($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : date('Y-m-d');
    
    // الحصول على الطلبات
    $args = array(
        'status' => array('wc-completed', 'wc-processing'),
        'date_created' => $start_date . '...' . $end_date,
        'limit' => -1
    );
    
    $orders = wc_get_orders($args);
    
    // إنشاء بيانات CSV
    $csv_data = array();
    
    // إضافة رأس الجدول
    $csv_data[] = array(
        'Order ID',
        'Date',
        'Customer Name',
        'Phone',
        'Address',
        'State',
        'Municipality',
        'Delivery Type',
        'Products',
        'Total',
        'Status'
    );
    
    // إضافة بيانات الطلبات
    foreach ($orders as $order) {
        $products = array();
        
        foreach ($order->get_items() as $item) {
            $product_name = $item->get_name();
            $quantity = $item->get_quantity();
            $products[] = $product_name . ' x ' . $quantity;
        }
        
        $csv_data[] = array(
            $order->get_order_number(),
            $order->get_date_created()->date('Y-m-d H:i:s'),
            $order->get_meta('_customer_full_name'),
            $order->get_billing_phone(),
            $order->get_billing_address_1(),
            $order->get_billing_state(),
            $order->get_billing_city(),
            $order->get_meta('delivery_type') === 'home' ? 'Home Delivery' : 'Office Delivery',
            implode(', ', $products),
            $order->get_total(),
            $order->get_status()
        );
    }
    
    // تحويل البيانات إلى CSV
    $csv_content = '';
    
    foreach ($csv_data as $row) {
        $csv_content .= '"' . implode('","', $row) . '"' . "\n";
    }
    
    // إرجاع محتوى CSV
    wp_send_json_success(array(
        'csv_content' => $csv_content,
        'filename' => 'orders-' . $start_date . '-to-' . $end_date . '.csv'
    ));
}
add_action('wp_ajax_export_orders_to_csv', 'export_orders_to_csv');
